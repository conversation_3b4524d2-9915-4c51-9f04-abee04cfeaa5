// test-upload-chunks.js
const fs = require("fs");
const FormData = require("form-data");
const fetch = require("node-fetch");

const FILE_PATH = "/home/<USER>/Downloads/recording-1750708428773.m4a";
const BACKEND_URL = "http://localhost:3000/api/v1/clinician/upload-audio-chunk";
// const BACKEND_URL = "https://api-stg.goscribble.ai/api/v1/clinician/upload-audio-chunk";
const CHUNK_SIZE = 0.5 * 1024 * 1024; // 5 MB

const assessmentId = "6890b69082039f49c386dada";
const tenantDb = "stg";
const token =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ2IjoyLCJ0eXBlIjoiYWNjZXNzIiwiaWQiOiI2ODM1ZTVmN2VkN2ZhNzI0Y2I3NDMxZDYiLCJyb2xlcyI6WyJjbGluaWNpYW4iXSwicGVybWlzc2lvbiI6WyJhc3Nlc3NtZW50LmNyZWF0ZSIsImFzc2Vzc21lbnQucmVhZCIsImFzc2Vzc21lbnQudXBkYXRlIiwiYXNzZXNzbWVudC5kZWxldGUiLCJhbnN3ZXIuY3JlYXRlIiwiYW5zd2VyLnJlYWQiLCJhbnN3ZXIudXBkYXRlIiwiYW5zd2VyLmRlbGV0ZSIsImZvcm0uY3JlYXRlIiwiZm9ybS5yZWFkIiwiZm9ybS51cGRhdGUiLCJmb3JtLmRlbGV0ZSIsInZpc2l0LmNyZWF0ZSIsInZpc2l0LnJlYWQiLCJ2aXNpdC51cGRhdGUiLCJzZWxmLnJlYWQiLCJzZWxmLnVwZGF0ZSJdLCJpc3N1ZXIiOiJTY3JpYmJsZSIsImlhdCI6MTc1ODc4MDMxMSwiZXhwIjoxNzU4NzgwOTExfQ.0zkik6t2-oAj1NEnJRz7NPfb6jAnEohMjNbGjL8n4ps";

async function uploadFileInChunks() {
  const stat = fs.statSync(FILE_PATH);
  const totalSize = stat.size;
  console.log(`File size: ${totalSize} bytes`);

  const fd = fs.openSync(FILE_PATH, "r");
  let partNumber = 1;
  let offset = 0;

  while (offset < totalSize) {
    const end = Math.min(offset + CHUNK_SIZE, totalSize);
    const chunkSize = end - offset;

    const buffer = Buffer.alloc(chunkSize);
    fs.readSync(fd, buffer, 0, chunkSize, offset);

    const form = new FormData();
    form.append("audio", buffer, {
      filename: `recording3.m4a`,
      contentType: "audio/mp4",
      knownLength: buffer.length,
    });
    form.append("assessmentId", assessmentId);
    form.append("partNumber", String(partNumber));
    form.append("isLastChunk", String(end === totalSize));

    console.log(`Uploading part ${partNumber}, size: ${chunkSize} bytes`);

    const res = await fetch(BACKEND_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "x-tenant-id": tenantDb,
        ...form.getHeaders(),
      },
      body: form,
    });

    const text = await res.text();
    console.log(`Response for part ${partNumber}: ${res.status} - ${text}`);

    offset = end;
    partNumber++;
  }

  fs.closeSync(fd);
  console.log("✅ Upload complete");
}

uploadFileInChunks().catch(console.error);
