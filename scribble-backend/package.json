{"name": "scribble-backend", "version": "1.0.0", "description": "Scribble application backend code", "type": "commonjs", "main": "index.js", "scripts": {"test": "jest --force<PERSON>xit", "start": "nodemon --max-old-space-size=2048 index", "lint": "eslint 'src' 'migrations'", "lint:fix": "eslint --fix 'src' 'migrations'", "prettier": "prettier \"**/*.{js,tsx,ts}\" -l", "prettier:fix": "prettier --write \"**/*.{js,tsx,ts}\"", "migrate:up": "migrate-mongo up", "migrate:down": "migrate-mongo down", "migrate:down:block": "migrate-mongo down --block", "migrate:status": "migrate-mongo status"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.893.0", "@jest/globals": "^29.7.0", "@slack/web-api": "^7.9.3", "aws-sdk": "^2.1692.0", "axios": "^1.8.3", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-useragent": "^1.0.15", "helmet": "^8.0.0", "http": "^0.0.1-security", "husky": "^9.1.7", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "luxon": "^3.7.1", "method-override": "^3.0.0", "migrate-mongo": "^12.1.3", "mocha": "^11.1.0", "moment": "^2.30.1", "mongoose": "^8.9.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nconf": "^0.12.1", "node-cache": "^5.1.2", "node-schedule": "^2.1.1", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "path": "^0.12.7", "qrcode": "^1.5.4", "serverless-http": "^3.2.0", "speakeasy": "^2.0.0", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.5", "validator": "^13.12.0", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.18.0", "eslint": "^9.18.0", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "serverless-offline": "^14.4.0", "supertest": "^7.1.0"}}