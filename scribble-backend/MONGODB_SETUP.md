# MongoDB Replica Set Setup

## Overview
This setup provides a 3-node MongoDB replica set that supports transactions and is accessible from both your Node.js application and MongoDB Compass.

## Architecture
- **mongo1** (Primary): `localhost:27017` - <PERSON><PERSON> reads and writes
- **mongo2** (Secondary): `localhost:27018` - <PERSON><PERSON> reads only
- **mongo3** (Secondary): `localhost:27019` - <PERSON><PERSON> reads only

## Starting the Replica Set

```bash
cd scribble-backend
docker-compose up -d
```

Wait about 30 seconds for the replica set to initialize completely.

## Connection Strings

### For Node.js Applications
```javascript
// Full replica set connection (recommended for production)
const uri = 'mongodb://localhost:27017,localhost:27018,localhost:27019/your_database?replicaSet=rs0';

// Single node connection (for development only)
const uri = 'mongodb://localhost:27017/your_database';
```

### For MongoDB Compass
1. Open MongoDB Compass
2. Use this connection string:
   ```
   mongodb://localhost:27017,localhost:27018,localhost:27019/?replicaSet=rs0
   ```
3. Or connect to individual nodes:
   - Primary: `mongodb://localhost:27017`
   - Secondary 1: `mongodb://localhost:27018`
   - Secondary 2: `mongodb://localhost:27019`

## Testing the Setup

### Quick Test
```bash
# Test basic connectivity
nc -zv localhost 27017 27018 27019

# Test MongoDB operations
docker exec mongo1 mongosh --eval "rs.status()"
```

### Node.js Test
```bash
cd scribble-backend
node test-connection.js
```

## Transaction Support
✅ **Transactions are fully supported** - This replica set configuration enables MongoDB transactions which are required for your application.

Example transaction code:
```javascript
const session = client.startSession();
try {
  await session.withTransaction(async () => {
    await collection1.insertOne(doc1, { session });
    await collection2.updateOne(filter, update, { session });
    // All operations will be committed together
  });
} finally {
  await session.endSession();
}
```

## Monitoring

### Check Replica Set Status
```bash
docker exec mongo1 mongosh --eval "rs.status()"
```

### Check Individual Node Status
```bash
docker logs mongo1
docker logs mongo2
docker logs mongo3
```

### View Container Status
```bash
docker ps
```

## Troubleshooting

### If containers won't start
```bash
docker-compose down -v
docker-compose up -d
```

### If replica set isn't working
```bash
# Check logs
docker logs mongo-setup

# Manually reinitialize (if needed)
docker exec mongo1 mongosh --eval "rs.initiate({
  _id: 'rs0',
  members: [
    { _id: 0, host: 'mongo1:27017', priority: 2 },
    { _id: 1, host: 'mongo2:27017', priority: 1 },
    { _id: 2, host: 'mongo3:27017', priority: 1 }
  ]
})"
```

## Production Notes
- Data is persisted in Docker volumes
- The primary node (mongo1) has higher priority for elections
- All nodes are configured with `--bind_ip_all` for external access
- No authentication is configured (add auth for production)

## Stopping the Setup
```bash
cd scribble-backend
docker-compose down
```

To also remove data volumes:
```bash
docker-compose down -v
```
