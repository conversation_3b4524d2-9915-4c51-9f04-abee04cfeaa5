/**
 * File streaming manager for handling audio chunk uploads
 *
 * This module provides a streaming approach to handle audio chunk uploads:
 * 1. Chunks are streamed to local temporary files as they arrive
 * 2. When the last chunk is received, the complete file is uploaded to S3
 * 3. Temporary files are automatically cleaned up after successful upload
 *
 * This approach is more reliable than multipart uploads for chunked audio files
 * and reduces the complexity of managing upload states across multiple requests.
 */

const fs = require("fs").promises;
const path = require("path");
const { uploadFile } = require("./aws.js");
const logger = require("./logger.js");

class FileStreamManager {
  constructor() {
    this.tempDir = path.join(__dirname, "../../temp");
    this.ensureTempDir();
  }

  /**
   * Ensure temp directory exists
   */
  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      logger.error(`Error creating temp directory: ${error.message}`);
    }
  }

  /**
   * Get temp file path for an assessment
   * @param {string} assessmentId - The assessment ID
   * @param {string} originalName - Original file name
   * @returns {string} Temp file path
   */
  getTempFilePath(assessmentId, originalName) {
    const safeFileName = originalName.replace(/[^a-zA-Z0-9.-]/g, "_");
    return path.join(this.tempDir, `${assessmentId}_${safeFileName}`);
  }

  /**
   * Append chunk to temp file
   * @param {string} assessmentId - The assessment ID
   * @param {Object} audioFile - The audio file object
   * @param {number} partNumber - The part number
   * @returns {Promise<Object>} Result object with success status
   */
  async appendChunk(assessmentId, audioFile, partNumber) {
    try {
      const tempFilePath = this.getTempFilePath(
        assessmentId,
        audioFile.originalname,
      );

      // For the first chunk, create the file
      if (partNumber === 1 || partNumber === "1") {
        await fs.writeFile(tempFilePath, audioFile.buffer);
        logger.debug(
          `Created temp file for assessment ${assessmentId}: ${tempFilePath}`,
        );
      } else {
        // For subsequent chunks, append to the file
        await fs.appendFile(tempFilePath, audioFile.buffer);
        logger.debug(
          `Appended chunk ${partNumber} to temp file for assessment ${assessmentId}`,
        );
      }

      return { success: true, tempFilePath };
    } catch (error) {
      logger.error(
        `Error appending chunk for assessment ${assessmentId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Upload complete file to S3 and clean up temp file
   * @param {string} assessmentId - The assessment ID
   * @param {string} originalName - Original file name
   * @param {string} keyPath - S3 key path
   * @param {string} contentType - File content type
   * @returns {Promise<Object>} Upload result with S3 location
   */
  async uploadCompleteFile(assessmentId, originalName, keyPath, contentType) {
    const tempFilePath = this.getTempFilePath(assessmentId, originalName);
    const s3Key = `${keyPath}/input/${originalName}`;

    try {
      // Read the complete file
      const fileBuffer = await fs.readFile(tempFilePath);

      // Create file object for upload
      const completeFile = {
        buffer: fileBuffer,
        originalname: originalName,
        mimetype: contentType,
        size: fileBuffer.length,
      };

      logger.debug(
        `Uploading complete file to S3: ${s3Key} (${fileBuffer.length} bytes)`,
      );

      // Upload to S3
      const uploadResult = await uploadFile(completeFile, s3Key);

      if (uploadResult && uploadResult.Location) {
        logger.debug(
          `Successfully uploaded complete file for assessment ${assessmentId}: ${uploadResult.Location}`,
        );

        // Clean up temp file
        // await this.cleanupTempFile(assessmentId, originalName);

        return { success: true, url: uploadResult.Location };
      }
      throw new Error("Upload failed - no location returned");
    } catch (error) {
      logger.error(
        `Error uploading complete file for assessment ${assessmentId}: ${error.message}`,
      );

      // Clean up temp file on error
      // await this.cleanupTempFile(assessmentId, originalName);
      throw error;
    }
  }

  /**
   * Clean up temporary file
   * @param {string} assessmentId - The assessment ID
   * @param {string} originalName - Original file name
   */
  async cleanupTempFile(assessmentId, originalName) {
    try {
      const tempFilePath = this.getTempFilePath(assessmentId, originalName);
      await fs.unlink(tempFilePath);
      logger.debug(
        `Cleaned up temp file for assessment ${assessmentId}: ${tempFilePath}`,
      );
    } catch (error) {
      // Don't throw error for cleanup failures, just log
      logger.warn(
        `Error cleaning up temp file for assessment ${assessmentId}: ${error.message}`,
      );
    }
  }

  /**
   * Check if temp file exists
   * @param {string} assessmentId - The assessment ID
   * @param {string} originalName - Original file name
   * @returns {Promise<boolean>} True if file exists
   */
  async tempFileExists(assessmentId, originalName) {
    try {
      const tempFilePath = this.getTempFilePath(assessmentId, originalName);
      await fs.access(tempFilePath);
      return true;
    } catch (error) {
      logger.error(
        `Error checking if temp file exists for assessment ${assessmentId}: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Get temp file size
   * @param {string} assessmentId - The assessment ID
   * @param {string} originalName - Original file name
   * @returns {Promise<number>} File size in bytes
   */
  async getTempFileSize(assessmentId, originalName) {
    try {
      const tempFilePath = this.getTempFilePath(assessmentId, originalName);
      const stats = await fs.stat(tempFilePath);
      return stats.size;
    } catch (error) {
      logger.error(
        `Error getting temp file size for assessment ${assessmentId}: ${error.message}`,
      );
      return 0;
    }
  }

  /**
   * Clean up all temp files (for maintenance)
   */
  async cleanupAllTempFiles() {
    try {
      const files = await fs.readdir(this.tempDir);
      const deletePromises = files.map((file) => {
        const filePath = path.join(this.tempDir, file);
        return fs.unlink(filePath).catch((error) => {
          logger.warn(`Error deleting temp file ${file}: ${error.message}`);
        });
      });

      await Promise.all(deletePromises);
      logger.debug(`Cleaned up ${files.length} temp files`);
    } catch (error) {
      logger.error(`Error cleaning up temp files: ${error.message}`);
    }
  }
}

// Singleton instance
const fileStreamManager = new FileStreamManager();

module.exports = fileStreamManager;
