/**
 * Upload state management for multipart uploads
 * In production, this should be replaced with Redis for persistence across server restarts
 */

class UploadStateManager {
  constructor() {
    this.uploadMap = new Map();
    this.cleanupInterval = null;
    this.startCleanupTimer();
  }

  /**
   * Store upload state for an assessment
   * @param {string} assessmentId - The assessment ID
   * @param {Object} state - Upload state object
   */
  setUploadState(assessmentId, state) {
    this.uploadMap.set(assessmentId, {
      ...state,
      lastAccessed: Date.now(),
    });
  }

  /**
   * Get upload state for an assessment
   * @param {string} assessmentId - The assessment ID
   * @returns {Object|null} Upload state or null if not found
   */
  getUploadState(assessmentId) {
    const state = this.uploadMap.get(assessmentId);
    if (state) {
      state.lastAccessed = Date.now();
      return state;
    }
    return null;
  }

  /**
   * Remove upload state for an assessment
   * @param {string} assessmentId - The assessment ID
   */
  removeUploadState(assessmentId) {
    this.uploadMap.delete(assessmentId);
  }

  /**
   * Check if upload state exists for an assessment
   * @param {string} assessmentId - The assessment ID
   * @returns {boolean} True if state exists
   */
  hasUploadState(assessmentId) {
    return this.uploadMap.has(assessmentId);
  }

  /**
   * Start cleanup timer to remove stale upload states
   * Removes uploads older than 24 hours
   */
  startCleanupTimer() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Clean up every hour
    this.cleanupInterval = setInterval(
      () => {
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours

        for (const [assessmentId, state] of this.uploadMap.entries()) {
          if (now - state.lastAccessed > maxAge) {
            this.uploadMap.delete(assessmentId);
            console.log(
              `Cleaned up stale upload state for assessment: ${assessmentId}`,
            );
          }
        }
      },
      60 * 60 * 1000,
    ); // Run every hour
  }

  /**
   * Stop cleanup timer
   */
  stopCleanupTimer() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Get all active upload states (for debugging)
   * @returns {Array} Array of active upload states
   */
  getAllStates() {
    return Array.from(this.uploadMap.entries()).map(([id, state]) => ({
      assessmentId: id,
      ...state,
    }));
  }

  /**
   * Add file data for an assessment part
   * @param {string} assessmentId - The assessment ID
   * @param {number} partNumber - The part number
   * @param {Buffer} buffer - The file data buffer
   */
  addFileData(assessmentId, partNumber, buffer) {
    const state = this.getUploadState(assessmentId);
    if (state) {
      if (!state.fileData) {
        state.fileData = new Map();
      }
      state.fileData.set(partNumber, buffer);
      this.setUploadState(assessmentId, state);
    }
  }

  /**
   * Get complete file data for reconstruction
   * @param {string} assessmentId - The assessment ID
   * @returns {Buffer|null} Complete file data or null if not available
   */
  getCompleteFileData(assessmentId) {
    const state = this.getUploadState(assessmentId);
    if (state && state.fileData) {
      const sortedParts = Array.from(state.fileData.entries()).sort(
        (a, b) => a[0] - b[0],
      );

      //eslint-disable-next-line
      const buffers = sortedParts.map(([partNumber, buffer]) => buffer);
      return Buffer.concat(buffers);
    }
    return null;
  }

  /**
   * Clear all upload states (for testing)
   */
  clearAll() {
    this.uploadMap.clear();
  }
}

// Singleton instance
const uploadStateManager = new UploadStateManager();

module.exports = uploadStateManager;
