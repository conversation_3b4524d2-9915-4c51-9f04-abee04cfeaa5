const AWS = require("aws-sdk");
const {
  S3Client,
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand,
} = require("@aws-sdk/client-s3");
const dotenv = require("dotenv");
const logger = require("../lib/logger.js");
// const nconf = require("nconf");
dotenv.config();

const REGION = process.env.AWS_REGION;
const ACCESS_KEY = process.env.AWS_ACCESS_KEY_ID;
const SECRET_KEY = process.env.AWS_SECRET_ACCESS_KEY;

AWS.config.update({
  region: REGION,
  accessKeyId: ACCESS_KEY,
  secretAccessKey: SECRET_KEY,
});

const s3 = new AWS.S3();
const s3Client = new S3Client({
  region: REGION,
  credentials: {
    accessKeyId: ACCESS_KEY,
    secretAccessKey: SECRET_KEY,
  },
});
const sqs = new AWS.SQS();

const createFolder = async (folderName) => {
  try {
    const params = {
      Bucket: process.env.S3_BUCKET,
      Key: folderName,
      ACL: "private", // Ensure it's a valid S3 object
    };

    await s3.putObject(params).promise();
    logger.info(`Folder created successfully: ${folderName}`);
  } catch (error) {
    logger.error(`Error creating folder: ${error}`);
  }
};

const uploadFile = async (file, folderName) => {
  try {
    const params = {
      Bucket: process.env.S3_BUCKET,
      Key: folderName,
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const data = await s3.upload(params).promise();
    logger.debug(`Audio file uploaded successfully: ${data.Location}`);
    return data;
  } catch (error) {
    if (error.message.includes("Unsupported body payload object")) {
      logger.error(
        `Error uploading file: Error: Unsupported body payload object`,
      );
    } else {
      logger.error(`Error uploading file: ${error}`);
    }
  }
};

const downloadFile = async (bucketName, key) => {
  const params = {
    Bucket: bucketName,
    Key: key,
  };

  const data = await s3.getObject(params).promise();
  return data;
};

const pushToQueue = async (queueUrl, message) => {
  const params = {
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify(message),
  };

  const data = await sqs.sendMessage(params).promise();
  return data;
};

const deleteMessageFromQueue = async (queueUrl, message) => {
  const deleteParams = {
    QueueUrl: queueUrl,
    ReceiptHandle: message.ReceiptHandle,
  };
  await sqs.deleteMessage(deleteParams).promise();
  logger.debug(`Message deleted from queue: ${queueUrl}`);
};

const subscribeToQueue = (queueUrl, cb) => {
  const params = {
    QueueUrl: queueUrl,
    WaitTimeSeconds: 20,
  };

  setInterval(() => {
    sqs.receiveMessage(params, (err, data) => {
      if (err) {
        return cb(err);
      }
      if (data.Messages.length > 0) {
        data.Messages.forEach((m) => {
          logger.debug(`------------------------------------`);
          logger.debug(`----------NEW SQS MESSAGE-----------`);
          logger.debug(`${JSON.stringify(m)}`);
          logger.debug(`------------------------------------`);
          cb(null, m);
        });
      } else {
        // logger.debug("No messages in RPA Data Queue");
      }
    });
  }, 2000);
};

// Multipart upload functions for streaming
const createMultipartUpload = async (bucket, key, contentType) => {
  try {
    const command = new CreateMultipartUploadCommand({
      Bucket: bucket,
      Key: key,
      ContentType: contentType,
    });
    const response = await s3Client.send(command);
    logger.debug(`Multipart upload created: ${response.UploadId}`);
    return response;
  } catch (error) {
    logger.error(`Error creating multipart upload: ${error.message}`);
    throw error;
  }
};

const uploadPart = async (bucket, key, partNumber, uploadId, body) => {
  try {
    const command = new UploadPartCommand({
      Bucket: bucket,
      Key: key,
      PartNumber: partNumber,
      UploadId: uploadId,
      Body: body,
    });
    const response = await s3Client.send(command);
    logger.debug(`Part ${partNumber} uploaded successfully`);
    return response;
  } catch (error) {
    logger.error(`Error uploading part ${partNumber}: ${error.message}`);
    throw error;
  }
};

const completeMultipartUpload = async (bucket, key, uploadId, parts) => {
  try {
    const command = new CompleteMultipartUploadCommand({
      Bucket: bucket,
      Key: key,
      UploadId: uploadId,
      MultipartUpload: { Parts: parts },
    });
    const response = await s3Client.send(command);
    logger.debug(`Multipart upload completed: ${response.Location}`);
    return response;
  } catch (error) {
    logger.error(`Error completing multipart upload: ${error.message}`);
    throw error;
  }
};

const abortMultipartUpload = async (bucket, key, uploadId) => {
  try {
    const command = new AbortMultipartUploadCommand({
      Bucket: bucket,
      Key: key,
      UploadId: uploadId,
    });
    await s3Client.send(command);
    logger.debug(`Multipart upload aborted: ${uploadId}`);
  } catch (error) {
    logger.error(`Error aborting multipart upload: ${error.message}`);
    throw error;
  }
};

const reconstructFileFromParts = async (bucket, key, parts) => {
  try {
    logger.debug(`Reconstructing file from ${parts.length} parts`);

    // For now, we'll return an empty buffer since we don't have the actual part data
    // In a real implementation, you would need to store the part data or download it
    // This is a placeholder implementation
    logger.warn(
      `File reconstruction not fully implemented - returning empty buffer`,
    );

    // Return a buffer that represents the concatenated parts
    // In practice, you'd need to either:
    // 1. Store the part data when uploading
    // 2. Download the parts from S3 (but they're not complete yet)
    // 3. Use a different strategy

    return Buffer.alloc(0); // Placeholder
  } catch (error) {
    logger.error(`Error reconstructing file from parts: ${error.message}`);
    throw error;
  }
};

module.exports = {
  createFolder,
  uploadFile,
  pushToQueue,
  subscribeToQueue,
  deleteMessageFromQueue,
  downloadFile,
  createMultipartUpload,
  uploadPart,
  completeMultipartUpload,
  abortMultipartUpload,
  reconstructFileFromParts,
  s3Client,
};
