module.exports = {
  "post/api/v1/auth/login": [
    {
      input: "email",
      isInvalid: (input) => typeof input !== "string",
      messageOnValidationFail: "email is invalid",
    },
    {
      input: "password",
      isInvalid: (input) => typeof input !== "string",
      messageOnValidationFail: "password is invalid",
    },
  ],
  "post/api/v1/auth/recover-password": [
    {
      input: "token",
      isInvalid: (input) => typeof input !== "string",
      messageOnValidationFail: "token is invalid",
    },
    {
      input: "newPassword",
      isInvalid: (input) => typeof input !== "string",
      messageOnValidationFail: "newPassword is invalid",
    },
  ],
  "post/api/v1/clinician/upload-audio": [
    {
      input: "assessmentId",
      isInvalid: (input) => typeof input !== "string",
      messageOnValidationFail: "assessmentId is invalid",
    },
  ],
  "post/api/v1/clinician/upload-audio-chunk": [
    {
      input: "assessmentId",
      isInvalid: (input) => typeof input !== "string",
      messageOnValidationFail: "assessmentId is invalid",
    },
  ],
};
