const express = require("express");
const { auth } = require("../lib/index.js");
const {
  processAudio,
  processAudioChunk,
  getUploadStates,
} = require("../controllers/clinician.js");
const multer = require("multer");
const logger = require("../lib/logger.js");
const clinicianRoutes = express.Router();
const { checkMissingInputs, validateInputs } = require("../middlewares");
const validExtensions = /\.(mp3|wav|webm|mp4|m4a|mkv)$/;
const validMimeTypes = [
  "audio/webm",
  "audio/mp3",
  "audio/wav",
  "audio/x-m4a",
  "video/mp4",
  "video/webm",
  "video/x-matroska",
  "audio/mp4",
];

const upload = multer({
  // limits: { fileSize: 100 * 1024 * 1024 }, // 100 MB limit
  fileFilter(req, file, cb) {
    logger.info(`File details: ${JSON.stringify(file)}`);

    if (!file) {
      return cb(new Error("No file uploaded."));
    }

    if (!validExtensions.test(file.originalname)) {
      logger.error(
        "Invalid file extension. Got: " +
          file.originalname +
          ". Allowed extensions: mp3, wav, webm, mp4, m4a, mkv.",
      );
      return cb(
        new Error(
          "Invalid file extension. Got: " +
            file.originalname +
            ". Allowed extensions: mp3, wav, webm, mp4, m4a, mkv",
        ),
      );
    }

    if (!validMimeTypes.includes(file.mimetype)) {
      logger.error(
        "Invalid file type. Got: " +
          file.mimetype +
          ". Allowed Types: audio/webm, audio/mp3, audio/mp4, audio/wav, audio/x-m4a, video/mp4, video/webm, video/x-matroska.",
      );
      return cb(
        new Error(
          "Invalid file type. Got: " +
            file.mimetype +
            ". Allowed Types: audio/webm, audio/mp3, audio/mp4, audio/wav, audio/x-m4a, video/mp4, video/webm, video/x-matroska.",
        ),
      );
    }

    cb(null, true);
  },
});

clinicianRoutes.post(
  "/upload-audio",
  auth.protect(["assessment.update"]),
  upload.single("audio"),
  checkMissingInputs,
  validateInputs,
  processAudio,
);

clinicianRoutes.post(
  "/upload-audio-chunk",
  auth.protect(["assessment.update"]),
  upload.single("audio"),
  checkMissingInputs,
  validateInputs,
  processAudioChunk,
);

// Debug endpoint to check upload states (remove in production)
clinicianRoutes.get(
  "/debug/upload-states",
  auth.protect(["assessment.update"]),
  getUploadStates,
);

module.exports = clinicianRoutes;
