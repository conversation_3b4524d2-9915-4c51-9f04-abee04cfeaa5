const { getTenantDB } = require("../lib/dbManager.js");
const { transformAssessmentForAI } = require("../lib/utils.js");
const Form = require("../model/tenant/form.js");
const Assessment = require("../model/tenant/assessment.js");
const Visit = require("../model/tenant/visit.js");
require("dotenv").config();
const {
  responses: { SuccessResponse },
  logger,
} = require("../lib/index.js");
const { ErrorResponse } = require("../lib/responses.js");
const { uploadFile, pushToQueue } = require("../lib/aws.js");
const AWS = require("aws-sdk");
const uploadStateManager = require("../lib/uploadState.js");
const fileStreamManager = require("../lib/fileStreamManager.js");

const uploadForm = async (assessment, keyPath) => {
  logger.debug(
    `Preparing form data for upload for assessment:  ${assessment._id}`,
  );
  const form = assessment.formId.question;
  const aiFormatForm = transformAssessmentForAI(form);
  const formBuffer = Buffer.from(JSON.stringify(aiFormatForm));
  if (!(formBuffer instanceof Buffer)) {
    logger.error(`Failed to convert form to buffer`);
    throw new Error("Error converting form to buffer");
  }

  logger.debug(
    `Preparing to upload form to S3 for assessment: ${assessment._id}`,
  );
  const params = {
    Bucket: process.env.S3_BUCKET,
    Key: `${keyPath}/input/questionForm.json`,
    Body: formBuffer,
    ContentType: "application/json",
  };

  const s3 = new AWS.S3();
  logger.debug(`Uploading form to S3`);
  const formUploadData = await s3.upload(params).promise();
  logger.debug(`Form uploaded successfully to: ${formUploadData.Location}`);
};

const uploadSingleAudio = async (audioFile, keyPath) => {
  try {
    logger.debug(`Uploading audio file to S3`);
    await uploadFile(audioFile, `${keyPath}/input/${audioFile.originalname}`);
  } catch (error) {
    throw new Error(`Error uploading audio: ${error.message}`);
  }
};

const getAssessment = async (connection, assessmentId) => {
  try {
    const AssessmentModel = Assessment(connection);
    const FormModel = Form(connection);

    const assessment = await AssessmentModel.findById(assessmentId).populate({
      path: "formId",
      model: FormModel,
    });

    if (!assessment) {
      logger.error(`Assessment not found with ID: ${assessmentId}`);
      throw new Error(`Assessment not found with ID: ${assessmentId}`);
    }
    return assessment;
  } catch (error) {
    throw new Error(`Error getting assessment: ${error.message}`);
  }
};

const sendMessageToAIQueue = async (
  connection,
  visit,
  assessmentId,
  audioFile,
  tenantDb,
  user,
) => {
  try {
    const VisitModel = Visit(connection);
    const visitDetails = await VisitModel.findById(visit._id);

    logger.debug(`Preparing message for AI processing queue`);
    const message = {
      audioFilePath: `${tenantDb}/${visit._id}/${assessmentId}/input/${audioFile.originalname}`,
      questionFormPath: `${tenantDb}/${visit._id}/${assessmentId}/input/questionForm.json`,
      user_id: user.email,
      client_id: visitDetails.clientId,
      visit_id: visit._id,
      id: assessmentId,
      assessment_id: assessmentId,
      company_id: tenantDb,
      transcribe_type: "deepgram",
      audio_files: [audioFile.originalname],
      question_files: ["questionForm.json"],
    };

    const queueUrl = process.env.AI_INPUT_QUEUE_URL;
    logger.debug(`Sending message to AI queue: ${queueUrl}`);
    logger.debug(`Message: ${JSON.stringify(message)}`);
    await pushToQueue(queueUrl, message);
    logger.debug(`Message successfully sent to AI queue`);
  } catch (error) {
    throw new Error(`Error sending message to AI queue: ${error.message}`);
  }
};

/**
 * Upload audio chunk using streaming approach
 * Streams chunks to local temp files and uploads complete file to S3 when last chunk is received
 * @param {string} keyPath - S3 key path for the file
 * @param {Object} audioFile - The audio file object with buffer and metadata
 * @param {string} assessmentId - The assessment ID
 * @param {number|string} partNumber - The part number of the chunk
 * @param {boolean} isLastChunk - Whether this is the last chunk
 * @returns {Promise<Object>} Result object with success status and optional URL
 */
const uploadAudioChunk = async (
  keyPath,
  audioFile,
  assessmentId,
  partNumber,
  isLastChunk,
) => {
  try {
    logger.debug(
      `Processing audio chunk: ${audioFile.originalname}, size: ${audioFile.size} bytes, part: ${partNumber}, isLast: ${isLastChunk}`,
    );

    // Stream chunk to local temp file
    await fileStreamManager.appendChunk(assessmentId, audioFile, partNumber);

    // If this is the last chunk, upload the complete file to S3
    if (isLastChunk === true) {
      logger.debug(
        `Last chunk received for assessment ${assessmentId}, uploading complete file to S3`,
      );

      const uploadResult = await fileStreamManager.uploadCompleteFile(
        assessmentId,
        audioFile.originalname,
        keyPath,
        audioFile.mimetype,
      );

      if (uploadResult && uploadResult.success) {
        logger.debug(
          `Successfully uploaded complete file for assessment ${assessmentId}: ${uploadResult.url}`,
        );
        return { success: true, url: uploadResult.url };
      }
      throw new Error("Failed to upload complete file to S3");
    }

    // For non-last chunks, just return success
    logger.debug(
      `Chunk ${partNumber} processed successfully for assessment ${assessmentId}`,
    );
    return { success: true, partNumber };
  } catch (error) {
    logger.error(
      `Error processing audio chunk for assessment ${assessmentId}: ${error.message}`,
    );

    // Clean up temp file on error
    try {
      await fileStreamManager.cleanupTempFile(
        assessmentId,
        audioFile.originalname,
      );
    } catch (cleanupError) {
      logger.warn(
        `Error cleaning up temp file for assessment ${assessmentId}: ${cleanupError.message}`,
      );
    }

    throw new Error(`Upload failed: ${error.message}`);
  }
};

const processAudio = async (req, res) => {
  try {
    logger.debug(
      `Starting Single audio processing for tenant: ${req.tenantDb}`,
    );

    const { assessmentId } = req.body;
    logger.debug(
      `Processing audawaio for assessment ID: ${assessmentId} for tenant: ${req.tenantDb}`,
    );

    const audioFile = req.file;
    logger.debug(
      `Received audio file: ${audioFile.originalname} (${Number(audioFile.size) / 1024 / 1024} MB) for tenant: ${req.tenantDb}`,
    );

    logger.debug(`Connecting to tenant database: ${req.tenantDb}`);
    const connection = await getTenantDB(req.tenantDb);
    const AssessmentModel = Assessment(connection);
    const VisitModel = Visit(connection);

    logger.debug(
      `Fetching assessment details for ID: ${assessmentId} for tenant: ${req.tenantDb}`,
    );

    const assessment = await getAssessment(connection, assessmentId);

    const visit = assessment.visitId;
    if (!visit) {
      logger.error(
        `Visit not found for assessment: ${assessmentId} on tenant ${req.tenantDb}`,
      );
      return res
        .status(404)
        .json(
          new ErrorResponse(
            `Visit not found for assessment: ${assessmentId} on tenant ${req.tenantDb}`,
          ),
        );
    }

    const keyPath = `${req.tenantDb}/${visit._id}/${assessmentId}`;

    await uploadSingleAudio(audioFile, keyPath, assessmentId);
    logger.debug(`Updating visit status to Inprogress for visit: ${visit._id}`);
    await VisitModel.findByIdAndUpdate(visit._id, {
      status: "In Progress",
    });

    await uploadForm(assessment, keyPath);

    await sendMessageToAIQueue(
      connection,
      visit,
      assessmentId,
      audioFile,
      req.tenantDb,
      req.user,
    );
    logger.debug(`Updating assessment status for assessment: ${assessmentId}`);
    await AssessmentModel.findByIdAndUpdate(assessmentId, {
      status: "Submitted to AI",
    });

    logger.debug(`Audio processing completed successfully`);
    return res
      .status(200)
      .json(new SuccessResponse("Audio file uploaded and message sent to SQS"));
  } catch (error) {
    logger.error(`Error processing audio: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const processAudioChunk = async (req, res) => {
  try {
    logger.debug(`Starting audio chunk processing for tenant: ${req.tenantDb}`);

    const { assessmentId, partNumber } = req.body;
    let { isLastChunk } = req.body;
    if (isLastChunk === "true") {
      isLastChunk = true;
    }

    logger.debug(
      `Processin${isLastChunk === true ? " " : " non"} last chunk number ${partNumber} for assessment ID: ${assessmentId} for tenant: ${req.tenantDb}`,
    );

    const audioFile = req.file;
    logger.debug(
      `Received audio file: ${audioFile.originalname} (${Number(audioFile.size) / 1024 / 1024} MB) for tenant: ${req.tenantDb}`,
    );

    logger.debug(`Connecting to tenant database: ${req.tenantDb}`);
    const connection = await getTenantDB(req.tenantDb);
    const AssessmentModel = Assessment(connection);
    const VisitModel = Visit(connection);

    logger.debug(
      `Fetching assessment details for ID: ${assessmentId} for tenant: ${req.tenantDb}`,
    );

    const assessment = await getAssessment(connection, assessmentId);

    const visit = assessment.visitId;
    if (!visit) {
      logger.error(
        `Visit not found for assessment: ${assessmentId} on tenant ${req.tenantDb}`,
      );
      return res
        .status(404)
        .json(
          new ErrorResponse(
            `Visit not found for assessment: ${assessmentId} on tenant ${req.tenantDb}`,
          ),
        );
    }

    const keyPath = `${req.tenantDb}/${visit._id}/${assessmentId}`;

    await uploadAudioChunk(
      keyPath,
      audioFile,
      assessmentId,
      partNumber,
      isLastChunk,
    );

    if (partNumber === 1 || partNumber === "1") {
      logger.debug(
        `Updating visit status to Inprogress for visit: ${visit._id}`,
      );
      await VisitModel.findByIdAndUpdate(visit._id, {
        status: "In Progress",
      });
    }

    const assessmentUpdateObject = {
      uploadedParts: partNumber,
      isLastChunk: isLastChunk,
    };

    if (isLastChunk === true) {
      logger.debug(
        `This is the last chunk for assessment ${assessmentId}. So uploading form and sending to AI`,
      );
      await uploadForm(assessment, keyPath);
      await sendMessageToAIQueue(
        connection,
        visit,
        assessmentId,
        audioFile,
        req.tenantDb,
        req.user,
      );
      assessmentUpdateObject.status = "Submitted to AI";
    }

    logger.debug(`Updating assessment status for assessment: ${assessmentId}`);
    await AssessmentModel.findByIdAndUpdate(
      assessmentId,
      assessmentUpdateObject,
    );

    logger.debug(`Audio processing completed successfully`);
    return res
      .status(200)
      .json(new SuccessResponse("Audio file uploaded and message sent to SQS"));
  } catch (error) {
    logger.error(`Error processing audio chunk: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

// Debug endpoint to check upload states (remove in production)
const getUploadStates = async (req, res) => {
  try {
    const states = uploadStateManager.getAllStates();
    return res
      .status(200)
      .json(new SuccessResponse("Upload states retrieved", states));
  } catch (error) {
    logger.error(`Error getting upload states: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

module.exports = {
  processAudio,
  processAudioChunk,
  getUploadStates,
};
