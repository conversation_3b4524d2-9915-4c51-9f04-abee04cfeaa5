#!/bin/bash

# Simple MongoDB Replica Set Startup Script for macOS
# This script starts MongoDB instances in background processes

set -e

# Configuration
REPLICA_SET_NAME="rs0"
DATA_DIR="./mongodb-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting MongoDB Replica Set with 3 nodes...${NC}"

# Create data directories
echo -e "${YELLOW}Creating data directories...${NC}"
mkdir -p "${DATA_DIR}/mongo1"
mkdir -p "${DATA_DIR}/mongo2"
mkdir -p "${DATA_DIR}/mongo3"
mkdir -p "${DATA_DIR}/logs"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}Port $port is already in use. Please stop any existing MongoDB instances.${NC}"
        echo "To stop existing instances, run: ./stop-mongo-replica.sh"
        exit 1
    fi
}

# Check if ports are available
echo -e "${YELLOW}Checking if ports are available...${NC}"
check_port 27017
check_port 27018
check_port 27019

echo -e "${YELLOW}Starting MongoDB instances...${NC}"

# Start MongoDB instance 1
echo "Starting mongo1 on port 27017..."
mongod --replSet "${REPLICA_SET_NAME}" --port 27017 --dbpath "$(pwd)/${DATA_DIR}/mongo1" --logpath "$(pwd)/${DATA_DIR}/logs/mongo1.log" --logappend --bind_ip localhost > /dev/null 2>&1 &
MONGO1_PID=$!
echo "mongo1 PID: $MONGO1_PID"

# Start MongoDB instance 2
echo "Starting mongo2 on port 27018..."
mongod --replSet "${REPLICA_SET_NAME}" --port 27018 --dbpath "$(pwd)/${DATA_DIR}/mongo2" --logpath "$(pwd)/${DATA_DIR}/logs/mongo2.log" --logappend --bind_ip localhost > /dev/null 2>&1 &
MONGO2_PID=$!
echo "mongo2 PID: $MONGO2_PID"

# Start MongoDB instance 3
echo "Starting mongo3 on port 27019..."
mongod --replSet "${REPLICA_SET_NAME}" --port 27019 --dbpath "$(pwd)/${DATA_DIR}/mongo3" --logpath "$(pwd)/${DATA_DIR}/logs/mongo3.log" --logappend --bind_ip localhost > /dev/null 2>&1 &
MONGO3_PID=$!
echo "mongo3 PID: $MONGO3_PID"

# Save PIDs for later cleanup
echo "$MONGO1_PID" > "${DATA_DIR}/mongo1.pid"
echo "$MONGO2_PID" > "${DATA_DIR}/mongo2.pid"
echo "$MONGO3_PID" > "${DATA_DIR}/mongo3.pid"

# Wait for instances to be ready
echo -e "${YELLOW}Waiting for MongoDB instances to be ready...${NC}"
sleep 10

# Check if instances are running
echo "Checking if instances are running..."
for port in 27017 27018 27019; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✓ MongoDB running on port $port${NC}"
    else
        echo -e "${RED}✗ MongoDB NOT running on port $port${NC}"
        echo "Check log files in ${DATA_DIR}/logs/"
        exit 1
    fi
done

# Initialize replica set
echo -e "${YELLOW}Initializing replica set...${NC}"
mongosh --port 27017 --eval "
rs.initiate({
  _id: '${REPLICA_SET_NAME}',
  members: [
    { _id: 0, host: 'localhost:27017', priority: 2 },
    { _id: 1, host: 'localhost:27018', priority: 1 },
    { _id: 2, host: 'localhost:27019', priority: 1 }
  ]
})
" --quiet

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Replica set initialized successfully!${NC}"
else
    echo -e "${RED}Failed to initialize replica set${NC}"
    exit 1
fi

# Wait for replica set to stabilize
echo -e "${YELLOW}Waiting for replica set to stabilize...${NC}"
sleep 15

# Check replica set status
echo -e "${YELLOW}Checking replica set status...${NC}"
mongosh --port 27017 --eval "rs.status()" --quiet

echo -e "${GREEN}"
echo "=========================================="
echo "MongoDB Replica Set is now running!"
echo "=========================================="
echo -e "${NC}"
echo "Connection details:"
echo "  Primary:     mongodb://localhost:27017"
echo "  Secondary 1: mongodb://localhost:27018"
echo "  Secondary 2: mongodb://localhost:27019"
echo ""
echo "Replica Set Connection String:"
echo "  mongodb://localhost:27017,localhost:27018,localhost:27019/?replicaSet=${REPLICA_SET_NAME}"
echo ""
echo "For MongoDB Compass:"
echo "  mongodb://localhost:27017,localhost:27018,localhost:27019/?replicaSet=${REPLICA_SET_NAME}"
echo ""
echo "To stop the replica set, run:"
echo "  ./stop-mongo-replica.sh"
echo ""
echo "Process IDs saved in:"
echo "  ${DATA_DIR}/mongo1.pid (PID: $MONGO1_PID)"
echo "  ${DATA_DIR}/mongo2.pid (PID: $MONGO2_PID)"
echo "  ${DATA_DIR}/mongo3.pid (PID: $MONGO3_PID)"
echo ""
echo "Log files:"
echo "  ${DATA_DIR}/logs/mongo1.log"
echo "  ${DATA_DIR}/logs/mongo2.log"
echo "  ${DATA_DIR}/logs/mongo3.log"
