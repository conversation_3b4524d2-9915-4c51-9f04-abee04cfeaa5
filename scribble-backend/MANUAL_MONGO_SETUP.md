# Manual MongoDB Replica Set Setup

Since you have `mongod` installed on your Mac, here's how to manually start a 3-node MongoDB replica set:

## Step 1: Navigate to the backend directory
```bash
cd scribble-backend
```

## Step 2: Create data directories
```bash
mkdir -p mongodb-data/mongo1 mongodb-data/mongo2 mongodb-data/mongo3 mongodb-data/logs
```

## Step 3: Open 3 separate terminal windows/tabs

### Terminal 1 - Start MongoDB Instance 1 (Primary)
```bash
cd scribble-backend
mongod --replSet rs0 --port 27017 --dbpath $(pwd)/mongodb-data/mongo1 --logpath $(pwd)/mongodb-data/logs/mongo1.log --logappend --bind_ip localhost
```

### Terminal 2 - Start MongoDB Instance 2 (Secondary)
```bash
cd scribble-backend
mongod --replSet rs0 --port 27018 --dbpath $(pwd)/mongodb-data/mongo2 --logpath $(pwd)/mongodb-data/logs/mongo2.log --logappend --bind_ip localhost
```

### Terminal 3 - Start MongoDB Instance 3 (Secondary)
```bash
cd scribble-backend
mongod --replSet rs0 --port 27019 --dbpath $(pwd)/mongodb-data/mongo3 --logpath $(pwd)/mongodb-data/logs/mongo3.log --logappend --bind_ip localhost
```

## Step 4: Initialize the Replica Set

In a 4th terminal window, run:

```bash
cd scribble-backend
mongosh --port 27017
```

Then in the MongoDB shell, run:

```javascript
rs.initiate({
  _id: 'rs0',
  members: [
    { _id: 0, host: 'localhost:27017', priority: 2 },
    { _id: 1, host: 'localhost:27018', priority: 1 },
    { _id: 2, host: 'localhost:27019', priority: 1 }
  ]
})
```

## Step 5: Verify the Setup

Wait about 10-15 seconds, then check the status:

```javascript
rs.status()
```

You should see all 3 members with one as PRIMARY and two as SECONDARY.

Type `exit` to leave the MongoDB shell.

## Connection Strings

### For your Node.js application:
```javascript
const uri = 'mongodb://localhost:27017,localhost:27018,localhost:27019/your_database?replicaSet=rs0';
```

### For MongoDB Compass:
```
mongodb://localhost:27017,localhost:27018,localhost:27019/?replicaSet=rs0
```

## Testing Transactions

Once the replica set is running, you can test transactions in your Node.js code:

```javascript
const session = client.startSession();
try {
  await session.withTransaction(async () => {
    await collection1.insertOne(doc1, { session });
    await collection2.updateOne(filter, update, { session });
    // All operations will be committed together
  });
} finally {
  await session.endSession();
}
```

## To Stop the Replica Set

1. In each of the 3 terminal windows running MongoDB, press `Ctrl+C`
2. Or run the stop script: `./stop-mongo-replica.sh`

## Troubleshooting

- If you get "port already in use" errors, make sure no other MongoDB instances are running
- Check the log files in `mongodb-data/logs/` if instances fail to start
- Make sure you have enough disk space for the database files

## Alternative: Use the Automated Script

If you prefer automation, you can try running:
```bash
./start-mongo-simple.sh
```

This script will start all 3 instances in the background and initialize the replica set automatically.
