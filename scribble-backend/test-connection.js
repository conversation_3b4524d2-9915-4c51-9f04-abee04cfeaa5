const { MongoClient } = require('mongodb');

// Connection string for replica set - using directConnection to avoid host discovery issues
const uri = 'mongodb://localhost:27017/scribble_admin?directConnection=true';

async function testConnection() {
  const client = new MongoClient(uri);
  
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('✅ Connected to MongoDB replica set successfully!');
    
    const db = client.db('scribble_admin');
    const collection = db.collection('testcollection');
    
    // Test basic operations
    console.log('\n📝 Testing basic operations...');
    
    // Insert a document
    const insertResult = await collection.insertOne({
      message: 'Test from Node.js',
      timestamp: new Date(),
      source: 'connection-test'
    });
    console.log('✅ Insert successful:', insertResult.insertedId);
    
    // Find documents
    const documents = await collection.find({}).toArray();
    console.log('✅ Found documents:', documents.length);
    
    // Test transactions
    console.log('\n🔄 Testing transactions...');
    const session = client.startSession();
    
    try {
      await session.withTransaction(async () => {
        await collection.insertOne({
          message: 'Transaction test 1',
          timestamp: new Date(),
          source: 'transaction-test'
        }, { session });
        
        await collection.insertOne({
          message: 'Transaction test 2',
          timestamp: new Date(),
          source: 'transaction-test'
        }, { session });
        
        console.log('✅ Transaction completed successfully!');
      });
    } finally {
      await session.endSession();
    }
    
    // Verify transaction results
    const transactionDocs = await collection.find({ source: 'transaction-test' }).toArray();
    console.log('✅ Transaction documents found:', transactionDocs.length);
    
    console.log('\n🎉 All tests passed! Your MongoDB replica set is ready for production.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.close();
  }
}

// Run the test
testConnection().catch(console.error);
