#!/bin/bash

# MongoDB Replica Set Stop Script
# This script stops all MongoDB replica set instances

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Stopping MongoDB Replica Set...${NC}"

# Function to stop MongoDB using PID file
stop_mongo_by_pid() {
    local node_name=$1
    local port=$2
    local pid_file="./mongodb-data/${node_name}.pid"

    echo -e "${YELLOW}Stopping ${node_name} on port ${port}...${NC}"

    # Try graceful shutdown first
    mongosh --port ${port} --eval "db.adminCommand('shutdown')" --quiet 2>/dev/null || true

    # Wait a moment
    sleep 2

    # Check PID file and kill if necessary
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file" 2>/dev/null || true)
        if [ ! -z "$pid" ]; then
            echo "Killing process with PID: $pid"
            kill -9 "$pid" 2>/dev/null || true
            rm -f "$pid_file"
        fi
    fi

    # Also check by port and force kill if necessary
    local pid=$(lsof -ti:${port} 2>/dev/null || true)
    if [ ! -z "$pid" ]; then
        echo -e "${YELLOW}Force killing process on port ${port}...${NC}"
        kill -9 $pid 2>/dev/null || true
    fi

    # Verify it's stopped
    if ! lsof -Pi :${port} -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}${node_name} stopped successfully${NC}"
    else
        echo -e "${RED}Failed to stop ${node_name} on port ${port}${NC}"
    fi
}

# Stop all MongoDB instances
stop_mongo_by_pid "mongo1" 27017
stop_mongo_by_pid "mongo2" 27018
stop_mongo_by_pid "mongo3" 27019

echo -e "${GREEN}"
echo "=========================================="
echo "MongoDB Replica Set stopped!"
echo "=========================================="
echo -e "${NC}"
echo "Data is preserved in ./mongodb-data/"
echo "To start again, run: ./start-mongo-replica.sh"
