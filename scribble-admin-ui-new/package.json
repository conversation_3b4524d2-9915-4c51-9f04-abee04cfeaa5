{"name": "scribble-admin-ui", "private": true, "version": "1.0.0", "description": "Modern Scribble Admin UI built with Vite, React 19, TypeScript, and MUI", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/x-charts": "^8.11.3", "@mui/x-data-grid": "^8.11.3", "@mui/x-date-pickers": "^8.11.3", "@reduxjs/toolkit": "^2.9.0", "axios": "^1.12.2", "date-fns": "^4.1.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "react-redux": "^9.2.0", "react-router-dom": "^7.9.2"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.3", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.44.0", "vite": "^7.1.7"}}