# Scribble Admin UI

A modern, responsive admin dashboard built with the latest web technologies for managing tenants, users, visits, and forms in the Scribble healthcare platform.

## 🚀 Tech Stack

- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Lightning-fast build tool and dev server
- **Material-UI (MUI)** - Modern React component library
- **Redux Toolkit** - Predictable state management
- **React Router v6** - Client-side routing
- **React Hook Form** - Performant forms with easy validation
- **Axios** - HTTP client for API calls
- **Date-fns** - Modern date utility library

## ✨ Features

- **Multi-tenant Architecture** - Support for multiple organizations
- **Role-based Access Control** - Super admin, tenant admin, and clinician roles
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile
- **Modern UI/UX** - Clean, intuitive interface with Material Design
- **Real-time Notifications** - Toast notifications for user feedback
- **Dark/Light Theme** - Customizable theme support
- **Type Safety** - Full TypeScript coverage
- **Performance Optimized** - Code splitting and lazy loading

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Common components (notifications, etc.)
│   └── layout/         # Layout components (sidebar, topbar, etc.)
├── pages/              # Page components
├── services/           # API service layer
├── store/              # Redux store and slices
├── types/              # TypeScript type definitions
├── App.tsx             # Main app component
└── main.tsx           # App entry point
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd scribble-admin-ui
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3001`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_TITLE=Scribble Admin
```

### API Integration

The app is configured to proxy API requests to `http://localhost:3000`. Update the Vite configuration in `vite.config.ts` to change the backend URL.

## 🎨 Customization

### Theme

The MUI theme can be customized in `src/App.tsx`. The current theme includes:

- Custom color palette
- Typography settings
- Component style overrides
- Responsive breakpoints

### Adding New Pages

1. Create a new component in `src/pages/`
2. Add the route in `src/App.tsx`
3. Update the sidebar navigation in `src/components/layout/Sidebar.tsx`

## 🔐 Authentication

The app supports role-based authentication with three user types:

- **Super Admin** - Manages tenants and global settings
- **Tenant Admin** - Manages users, visits, and forms within their tenant
- **Clinician** - Access to visits and forms

## 📱 Responsive Design

The application is fully responsive and optimized for:

- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

### Docker Deployment

```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.
