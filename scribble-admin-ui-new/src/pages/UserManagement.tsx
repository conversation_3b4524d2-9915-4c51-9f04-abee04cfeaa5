import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';

const UserManagement: React.FC = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          User Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{ borderRadius: 2 }}
        >
          Add User
        </Button>
      </Box>

      <Paper sx={{ p: 3, minHeight: 400 }}>
        <Typography variant="h6" gutterBottom>
          User List
        </Typography>
        <Typography color="text.secondary">
          User management functionality will be implemented here.
        </Typography>
      </Paper>
    </Box>
  );
};

export default UserManagement;
