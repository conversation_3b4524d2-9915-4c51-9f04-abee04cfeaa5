import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

const Profile: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
        Profile
      </Typography>

      <Paper sx={{ p: 3, minHeight: 400 }}>
        <Typography variant="h6" gutterBottom>
          User Profile
        </Typography>
        <Typography color="text.secondary">
          Profile management functionality will be implemented here.
        </Typography>
      </Paper>
    </Box>
  );
};

export default Profile;
