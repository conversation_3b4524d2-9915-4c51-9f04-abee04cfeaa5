import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Alert, AlertColor } from '@mui/material';
import { useAppDispatch, useAppSelector } from '@/store';
import { removeNotification } from '@/store/slices/notificationSlice';

const NotificationSnackbar: React.FC = () => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector((state) => state.notification);

  const handleClose = (id: string) => {
    dispatch(removeNotification(id));
  };

  useEffect(() => {
    notifications.forEach((notification) => {
      if (notification.autoHide && notification.duration) {
        const timer = setTimeout(() => {
          dispatch(removeNotification(notification.id));
        }, notification.duration);

        return () => clearTimeout(timer);
      }
    });
  }, [notifications, dispatch]);

  return (
    <>
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{
            mt: index * 7, // Stack notifications
          }}
        >
          <Alert
            onClose={() => handleClose(notification.id)}
            severity={notification.type as AlertColor}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
};

export default NotificationSnackbar;
