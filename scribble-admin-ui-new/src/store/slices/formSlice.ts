import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Form, FormTemplate } from '@/types';
import { formService } from '@/services/formService';

interface FormState {
  forms: Form[];
  templates: FormTemplate[];
  loading: boolean;
  error: string | null;
}

const initialState: FormState = {
  forms: [],
  templates: [],
  loading: false,
  error: null,
};

export const fetchFormsAsync = createAsyncThunk(
  'form/fetchForms',
  async (_, { rejectWithValue }) => {
    try {
      const response = await formService.getForms();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch forms');
    }
  }
);

export const fetchTemplatesAsync = createAsyncThunk(
  'form/fetchTemplates',
  async (_, { rejectWithValue }) => {
    try {
      const response = await formService.getTemplates();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch templates');
    }
  }
);

const formSlice = createSlice({
  name: 'form',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFormsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFormsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.forms = action.payload;
      })
      .addCase(fetchFormsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchTemplatesAsync.fulfilled, (state, action) => {
        state.templates = action.payload;
      });
  },
});

export const { clearError } = formSlice.actions;
export default formSlice.reducer;
