import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Tenant } from '@/types';
import { tenantService } from '@/services/tenantService';

interface TenantState {
  tenants: Tenant[];
  currentTenant: Tenant | null;
  loading: boolean;
  error: string | null;
}

const initialState: TenantState = {
  tenants: [],
  currentTenant: null,
  loading: false,
  error: null,
};

export const fetchTenantsAsync = createAsyncThunk(
  'tenant/fetchTenants',
  async (_, { rejectWithValue }) => {
    try {
      const response = await tenantService.getTenants();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tenants');
    }
  }
);

export const createTenantAsync = createAsyncThunk(
  'tenant/createTenant',
  async (tenantData: Partial<Tenant>, { rejectWithValue }) => {
    try {
      const response = await tenantService.createTenant(tenantData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create tenant');
    }
  }
);

const tenantSlice = createSlice({
  name: 'tenant',
  initialState,
  reducers: {
    setCurrentTenant: (state, action) => {
      state.currentTenant = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTenantsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTenantsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.tenants = action.payload;
      })
      .addCase(fetchTenantsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createTenantAsync.fulfilled, (state, action) => {
        state.tenants.push(action.payload);
      });
  },
});

export const { setCurrentTenant, clearError } = tenantSlice.actions;
export default tenantSlice.reducer;
