import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Visit } from '@/types';
import { visitService } from '@/services/visitService';

interface VisitState {
  visits: Visit[];
  loading: boolean;
  error: string | null;
}

const initialState: VisitState = {
  visits: [],
  loading: false,
  error: null,
};

export const fetchVisitsAsync = createAsyncThunk(
  'visit/fetchVisits',
  async (_, { rejectWithValue }) => {
    try {
      const response = await visitService.getVisits();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch visits');
    }
  }
);

const visitSlice = createSlice({
  name: 'visit',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchVisitsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVisitsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.visits = action.payload;
      })
      .addCase(fetchVisitsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError } = visitSlice.actions;
export default visitSlice.reducer;
