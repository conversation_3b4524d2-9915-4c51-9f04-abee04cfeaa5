import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AuthState, LoginCredentials, User } from '@/types';
import { authService } from '@/services/authService';

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  userType: null,
  token: localStorage.getItem('token'),
};

// Async thunks
export const loginAsync = createAsyncThunk(
  'auth/login',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      const response = await authService.login(credentials);
      localStorage.setItem('token', response.accessToken);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Login failed');
    }
  }
);

export const logoutAsync = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout();
      localStorage.removeItem('token');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Logout failed');
    }
  }
);

export const refreshTokenAsync = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authService.refreshToken();
      localStorage.setItem('token', response.accessToken);
      return response;
    } catch (error: any) {
      localStorage.removeItem('token');
      return rejectWithValue(error.message || 'Token refresh failed');
    }
  }
);

export const getCurrentUserAsync = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authService.getCurrentUser();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to get user');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearAuth: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.userType = null;
      state.token = null;
      localStorage.removeItem('token');
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      // Determine user type based on role
      if (action.payload.roleName === 'admin') {
        state.userType = 'tenant_admin';
      } else if (action.payload.roleName === 'clinician') {
        state.userType = 'clinician';
      } else {
        state.userType = 'super_admin';
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.isAuthenticated = true;
        state.token = action.payload.accessToken;
        state.user = action.payload.user;
        // Determine user type based on role or other criteria
        if (action.payload.user?.roleName === 'admin') {
          state.userType = 'tenant_admin';
        } else if (action.payload.user?.roleName === 'clinician') {
          state.userType = 'clinician';
        } else {
          state.userType = 'super_admin';
        }
      })
      .addCase(loginAsync.rejected, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.userType = null;
        state.token = null;
      })
      // Logout
      .addCase(logoutAsync.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.userType = null;
        state.token = null;
      })
      // Refresh token
      .addCase(refreshTokenAsync.fulfilled, (state, action) => {
        state.token = action.payload.accessToken;
        state.isAuthenticated = true;
      })
      .addCase(refreshTokenAsync.rejected, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.userType = null;
        state.token = null;
      })
      // Get current user
      .addCase(getCurrentUserAsync.fulfilled, (state, action) => {
        state.user = action.payload;
        state.isAuthenticated = true;
      });
  },
});

export const { clearAuth, setUser } = authSlice.actions;
export default authSlice.reducer;
