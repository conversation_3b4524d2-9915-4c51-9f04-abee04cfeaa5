import { LoginCredentials, User, LoginResponse, ApiResponse } from '@/types';
import { apiService } from './api';

class AuthService {
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiService.post<ApiResponse<LoginResponse>>('/auth/login', credentials);
    return response.data.data;
  }

  async logout() {
    const response = await apiService.post('/auth/logout');
    return response.data.data;
  }

  async refreshToken(): Promise<LoginResponse> {
    const refreshToken = localStorage.getItem('refreshToken');
    const response = await apiService.post<ApiResponse<LoginResponse>>('/auth/refresh', { refreshToken });
    return response.data.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<ApiResponse<User>>('/auth/me');
    return response.data.data;
  }

  async changePassword(data: { currentPassword: string; newPassword: string }) {
    const response = await apiService.put('/auth/change-password', data);
    return response;
  }

  async updateProfile(data: Partial<User>) {
    const response = await apiService.put('/auth/me', data);
    return response;
  }

  async sendPasswordResetEmail(email: string) {
    const response = await apiService.post('/auth/recover-password-email', { email });
    return response;
  }

  async resetPassword(data: { token: string; newPassword: string }) {
    const response = await apiService.post('/auth/recover-password', data);
    return response;
  }
}

export const authService = new AuthService();
export default authService;
