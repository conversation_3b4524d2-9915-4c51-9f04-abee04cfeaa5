import { User } from '@/types';
import { apiService } from './api';

class UserService {
  async getUsers() {
    const response = await apiService.get('/auth/user');
    return response.data;
  }

  async createUser(data: Partial<User>) {
    const response = await apiService.post('/auth/user', data);
    return response.data;
  }

  async updateUser(id: string, data: Partial<User>) {
    const response = await apiService.put(`/auth/user/${id}`, data);
    return response;
  }

  async deleteUser(id: string) {
    const response = await apiService.delete(`/auth/user/${id}`);
    return response;
  }

  async getUserById(id: string) {
    const response = await apiService.get(`/auth/user/${id}`);
    return response;
  }

  async getRoles() {
    const response = await apiService.get('/auth/roles');
    return response;
  }
}

export const userService = new UserService();
export default userService;
