import { Tenant } from '@/types';
import { apiService } from './api';

class TenantService {
  async getTenants() {
    const response = await apiService.get('/auth/tenant');
    return response.data;
  }

  async createTenant(data: Partial<Tenant>) {
    const response = await apiService.post('/auth/tenant', data);
    return response.data;
  }

  async updateTenant(id: string, data: Partial<Tenant>) {
    const response = await apiService.put(`/auth/tenant/${id}`, data);
    return response.data;
  }

  async deleteTenant(id: string) {
    const response = await apiService.delete(`/auth/tenant/${id}`);
    return response.data;
  }

  async getTenantById(id: string) {
    const response = await apiService.get(`/auth/tenant/${id}`);
    return response;
  }
}

export const tenantService = new TenantService();
export default tenantService;
