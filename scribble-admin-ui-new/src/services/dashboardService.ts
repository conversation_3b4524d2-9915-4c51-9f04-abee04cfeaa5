import { DashboardStats, ApiResponse } from '@/types';
import { apiService } from './api';

class DashboardService {
  async getStats(): Promise<DashboardStats> {
    const response = await apiService.get<ApiResponse<DashboardStats>>('/dashboard/stats');
    return response.data.data;
  }

  async getRecentActivity() {
    const response = await apiService.get('/dashboard/activity');
    return response.data.data;
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
