import { Form, FormTemplate } from '@/types';
import { apiService } from './api';

class FormService {
  async getForms() {
    const response = await apiService.get('/visit/form');
    return response.data;
  }

  async createForm(data: Partial<Form>) {
    const response = await apiService.post('/visit/form', data);
    return response.data;
  }

  async updateForm(id: string, data: Partial<Form>) {
    const response = await apiService.put(`/visit/form/${id}`, data);
    return response.data;
  }

  async deleteForm(id: string) {
    const response = await apiService.delete(`/visit/form/${id}`);
    return response.data;
  }

  async getFormById(id: string) {
    const response = await apiService.get(`/visit/form/${id}`);
    return response.data;
  }

  async getTemplates() {
    const response = await apiService.get('/visit/formtemplate');
    return response.data;
  }

  async createTemplate(data: Partial<FormTemplate>) {
    const response = await apiService.post('/visit/formtemplate', data);
    return response.data;
  }

  async updateTemplate(id: string, data: Partial<FormTemplate>) {
    const response = await apiService.put(`/visit/formtemplate/${id}`, data);
    return response.data;
  }

  async deleteTemplate(id: string) {
    const response = await apiService.delete(`/visit/formtemplate/${id}`);
    return response.data;
  }
}

export const formService = new FormService();
export default formService;
