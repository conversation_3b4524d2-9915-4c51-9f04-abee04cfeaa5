# Scribble Admin UI

A comprehensive React-based admin interface for the Scribble healthcare platform. This application provides role-based access control for super administrators, tenant administrators, and clinicians to manage tenants, users, visits, forms, and system settings.

## Features

### 🔐 Authentication & Authorization
- **Multi-level Authentication**: Support for super admin and tenant-specific logins
- **Role-based Access Control**: Different interfaces for super admins, tenant admins, and clinicians
- **JWT Token Management**: Automatic token refresh and secure session handling
- **MFA Support**: Multi-factor authentication integration (ready for backend implementation)

### 👥 User Management
- **Super Admin Features**:
  - Create and manage tenants
  - View system-wide analytics
  - Tenant oversight and administration
- **Tenant Admin Features**:
  - User creation and management within tenant
  - Role assignment and permissions
  - Tenant-specific dashboard and analytics
- **Clinician Features**:
  - Visit and form management
  - Patient data access (role-dependent)

### 📊 Dashboard & Analytics
- **Real-time KPIs**: Client count, active clinicians, visits, forms
- **Interactive Charts**: Visual representation of key metrics
- **Recent Activity**: Quick overview of recent users, visits, and system activity
- **Quick Actions**: Fast access to common administrative tasks

### 🏥 Visit Management
- **Visit CRUD Operations**: Create, read, update, delete patient visits
- **Episode Management**: Track patient episodes and care continuity
- **Status Tracking**: Monitor visit progress and completion
- **Discipline & Form Type Management**: Organize visits by medical disciplines

### 📝 Form Management
- **Dynamic Form Builder**: Create and manage form templates
- **Form Instance Management**: Handle individual form submissions
- **Template Versioning**: Track form template changes over time
- **Field Configuration**: Flexible form field types and validation

### ⚙️ Settings & Configuration
- **User Preferences**: Personal settings and customization
- **Security Settings**: Password management and MFA configuration
- **Notification Preferences**: Email, SMS, and push notification settings
- **System Configuration**: Organization-wide settings and preferences

## Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI) v5
- **State Management**: Redux Toolkit with RTK Query
- **Routing**: React Router v6
- **Form Handling**: React Hook Form
- **HTTP Client**: Axios with interceptors
- **Build Tool**: Create React App
- **Styling**: Emotion (CSS-in-JS)

## Getting Started

### Prerequisites

- Node.js 16+ and npm/yarn
- Scribble Backend running on `http://localhost:3000`

### Installation

1. **Clone and navigate to the project**:
   ```bash
   cd scribble-admin-ui
   ```

2. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**:
   ```bash
   npm start
   # or
   yarn start
   ```

4. **Open your browser**:
   Navigate to `http://localhost:3001` (or the port shown in terminal)

### Environment Configuration

The application is configured to proxy API requests to `http://localhost:3000` by default. If your backend runs on a different port, update the `proxy` field in `package.json`.

## Usage

### Login Credentials

#### Super Admin Login
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`
- **Type**: Super Admin (no tenant ID required)

#### Tenant Admin Login
- **Email**: Your tenant-specific admin email
- **Password**: Your tenant-specific password
- **Tenant ID**: Your tenant's unique identifier

### Navigation

The application provides different navigation options based on user roles:

- **Super Admins**: Access to tenant management, system overview, and global settings
- **Tenant Admins**: Access to user management, visit management, form management, and tenant-specific analytics
- **Clinicians**: Access to visit management, form management, and personal settings

### Key Workflows

1. **Creating a Tenant** (Super Admin):
   - Navigate to Tenant Management
   - Click "Create Tenant"
   - Fill in tenant details and submit

2. **Adding Users** (Tenant Admin):
   - Navigate to User Management
   - Click "Add User"
   - Assign appropriate role and permissions

3. **Managing Visits** (Tenant Admin/Clinician):
   - Navigate to Visit Management
   - Create new visits or edit existing ones
   - Track visit status and progress

4. **Form Management** (Tenant Admin/Clinician):
   - Navigate to Form Management
   - Create form templates or manage form instances
   - Configure form fields and validation

## API Integration

The application integrates with the Scribble backend APIs:

- **Authentication**: `/api/v1/auth/*`
- **Tenant Management**: `/api/v1/auth/tenant`
- **User Management**: `/api/v1/auth/user`
- **Visit Management**: `/api/v1/visit/*`
- **Form Management**: `/api/v1/visit/form*`
- **Dashboard**: `/api/v1/dashboard/admin`

## Development

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Common components (notifications, etc.)
│   └── layout/         # Layout components (sidebar, topbar)
├── pages/              # Page components
├── services/           # API service classes
├── store/              # Redux store and slices
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── hooks/              # Custom React hooks
```

### Adding New Features

1. **Create Types**: Define TypeScript interfaces in `src/types/`
2. **Add API Service**: Create service methods in `src/services/`
3. **Create Redux Slice**: Add state management in `src/store/slices/`
4. **Build Components**: Create UI components in `src/components/` or `src/pages/`
5. **Add Routes**: Update routing in `src/App.tsx`

### Code Style

- Use TypeScript for all new code
- Follow Material-UI design patterns
- Use Redux Toolkit for state management
- Implement proper error handling and loading states
- Add proper TypeScript types for all props and state

## Deployment

### Build for Production

```bash
npm run build
# or
yarn build
```

### Environment Variables

Create a `.env` file for environment-specific configuration:

```env
REACT_APP_API_BASE_URL=https://your-api-domain.com
REACT_APP_VERSION=1.0.0
```

## Contributing

1. Follow the existing code style and patterns
2. Add proper TypeScript types
3. Include error handling and loading states
4. Test your changes thoroughly
5. Update documentation as needed

## Support

For issues and questions:
1. Check the backend API documentation
2. Verify backend connectivity
3. Check browser console for errors
4. Ensure proper authentication tokens

## License

This project is part of the Scribble healthcare platform. All rights reserved.
