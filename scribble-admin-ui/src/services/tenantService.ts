import { apiClient } from './api';
import { Tenant, CreateTenantRequest, PaginatedResponse } from '@/types';

export class TenantService {
  async getTenants(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<Tenant>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/auth/tenant?page=${page}&limit=${limit}`);
      
      // Transform response to match our expected format
      return {
        data: response.tenants || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch tenants');
    }
  }

  async getTenantById(id: string): Promise<Tenant> {
    try {
      const response = await apiClient.get(`/api/v1/auth/tenant/${id}`);
      return response.tenant;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch tenant');
    }
  }

  async createTenant(tenantData: CreateTenantRequest): Promise<Tenant> {
    try {
      const response = await apiClient.post('/api/v1/auth/tenant', tenantData);
      return response.tenant;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to create tenant');
    }
  }

  async updateTenant(id: string, data: Partial<Tenant>): Promise<Tenant> {
    try {
      const response = await apiClient.put(`/api/v1/auth/tenant/${id}`, data);
      return response.tenant;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update tenant');
    }
  }

  async deleteTenant(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/v1/auth/tenant/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to delete tenant');
    }
  }
}

export const tenantService = new TenantService();
