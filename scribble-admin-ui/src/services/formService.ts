import { apiClient } from './api';
import { Form, FormTemplate, FormType, Discipline, PaginatedResponse } from '@/types';

export class FormService {
  async getForms(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<Form>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/visit/form?page=${page}&limit=${limit}`);
      
      return {
        data: response.forms || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch forms');
    }
  }

  async getFormById(id: string): Promise<Form> {
    try {
      const response = await apiClient.get(`/api/v1/visit/form/${id}`);
      return response.form;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch form');
    }
  }

  async createForm(formData: Partial<Form>): Promise<Form> {
    try {
      const response = await apiClient.post('/api/v1/visit/form', formData);
      return response.form;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to create form');
    }
  }

  async updateForm(id: string, data: Partial<Form>): Promise<Form> {
    try {
      const response = await apiClient.put(`/api/v1/visit/form/${id}`, data);
      return response.form;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update form');
    }
  }

  async deleteForm(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/v1/visit/form/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to delete form');
    }
  }

  async getFormTemplates(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<FormTemplate>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/visit/formtemplate?page=${page}&limit=${limit}`);
      
      return {
        data: response.formTemplates || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch form templates');
    }
  }

  async getFormTemplateById(id: string): Promise<FormTemplate> {
    try {
      const response = await apiClient.get(`/api/v1/visit/formtemplate/${id}`);
      return response.formTemplate;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch form template');
    }
  }

  async createFormTemplate(templateData: Partial<FormTemplate>): Promise<FormTemplate> {
    try {
      const response = await apiClient.post('/api/v1/visit/formtemplate', templateData);
      return response.formTemplate;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to create form template');
    }
  }

  async updateFormTemplate(id: string, data: Partial<FormTemplate>): Promise<FormTemplate> {
    try {
      const response = await apiClient.put(`/api/v1/visit/formtemplate/${id}`, data);
      return response.formTemplate;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update form template');
    }
  }

  async getFormTypes(): Promise<FormType[]> {
    try {
      const response = await apiClient.get('/api/v1/visit/formtypes');
      return response.formTypes || [];
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch form types');
    }
  }

  async getDisciplines(): Promise<Discipline[]> {
    try {
      const response = await apiClient.get('/api/v1/visit/discipline');
      return response.disciplines || [];
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch disciplines');
    }
  }
}

export const formService = new FormService();
