import { apiClient } from './api';
import { User, Role, PaginatedResponse } from '@/types';

export class UserService {
  async getUsers(params: { page?: number; limit?: number; filters?: string } = {}): Promise<PaginatedResponse<User>> {
    try {
      const { page = 1, limit = 10, filters = '' } = params;
      const response = await apiClient.get(`/api/v1/auth/user?page=${page}&limit=${limit}${filters}`);
      
      return {
        data: response.users || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch users');
    }
  }

  async getUserById(id: string): Promise<User> {
    try {
      const response = await apiClient.get(`/api/v1/auth/user/${id}`);
      return response.user;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch user');
    }
  }

  async createUser(userData: { email: string; password: string; roleId: string }): Promise<User> {
    try {
      const response = await apiClient.post('/api/v1/auth/user', userData);
      return response.user;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to create user');
    }
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put(`/api/v1/auth/user/${id}`, data);
      return response.user;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update user');
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/v1/auth/user/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to delete user');
    }
  }

  async getRoles(): Promise<Role[]> {
    try {
      const response = await apiClient.get('/api/v1/auth/roles');
      return response.roles || [];
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch roles');
    }
  }

  async getClients(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<any>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/user/client?page=${page}&limit=${limit}`);
      
      return {
        data: response.clients || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch clients');
    }
  }

  async getClinicians(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<any>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/user/clinician?page=${page}&limit=${limit}`);
      
      return {
        data: response.clinicians || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch clinicians');
    }
  }

  async updateClinician(id: string, data: any): Promise<any> {
    try {
      const response = await apiClient.put(`/api/v1/user/clinician/${id}`, data);
      return response.clinician;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update clinician');
    }
  }
}

export const userService = new UserService();
