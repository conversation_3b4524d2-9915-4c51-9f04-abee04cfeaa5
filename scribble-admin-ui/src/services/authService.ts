import { apiClient } from './api';
import { LoginRequest, LoginResponse, User, AdminUser } from '@/types';

export class AuthService {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // Determine if this is a super admin login or tenant login
      if (credentials.tenantId) {
        // Tenant login - use v2 API or regular API with tenant header
        const response = await apiClient.post('/api/v1/auth/login', {
          email: credentials.email,
          password: credentials.password,
        }, {
          headers: {
            'x-tenant-id': credentials.tenantId,
          },
        });

        return {
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          user: response.user,
          userType: this.determineUserType(response.user),
          tenantId: credentials.tenantId,
        };
      } else {
        // Super admin login
        const response = await apiClient.post('/api/v1/auth/login', {
          email: credentials.email,
          password: credentials.password,
        });

        return {
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          user: response.user,
          userType: 'super_admin',
        };
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Login failed');
    }
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post('/api/v1/auth/logout');
    } catch (error) {
      // Even if logout fails on server, we should clear local state
      console.warn('Logout request failed, but clearing local state');
    }
  }

  async refreshToken(): Promise<{ accessToken: string }> {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await apiClient.post('/api/v1/auth/refresh', {
        refreshToken,
      });

      return {
        accessToken: response.accessToken,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Token refresh failed');
    }
  }

  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<void> {
    try {
      await apiClient.put('/api/v1/auth/change-password', {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Password change failed');
    }
  }

  async updateProfile(data: Partial<User | AdminUser>): Promise<User | AdminUser> {
    try {
      const response = await apiClient.put('/api/v1/auth/me', data);
      return response.user;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Profile update failed');
    }
  }

  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      await apiClient.post('/api/v1/auth/recover-password-email', { email });
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Password reset email failed');
    }
  }

  async resetPassword(data: { token: string; newPassword: string }): Promise<void> {
    try {
      await apiClient.post('/api/v1/auth/recover-password', {
        token: data.token,
        newPassword: data.newPassword,
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Password reset failed');
    }
  }

  private determineUserType(user: any): 'super_admin' | 'tenant_admin' | 'clinician' {
    if (user.permission && user.permission.includes('tenant.create')) {
      return 'super_admin';
    }
    
    if (user.roleId?.name === 'admin') {
      return 'tenant_admin';
    }
    
    return 'clinician';
  }
}

export const authService = new AuthService();
