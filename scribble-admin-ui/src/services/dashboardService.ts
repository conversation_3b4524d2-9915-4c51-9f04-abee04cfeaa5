import { apiClient } from './api';
import { DashboardKPIs } from '@/types';

export class DashboardService {
  async getKPIs(): Promise<DashboardKPIs> {
    try {
      const response = await apiClient.get('/api/v1/dashboard/admin');
      return response;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch dashboard KPIs');
    }
  }
}

export const dashboardService = new DashboardService();
