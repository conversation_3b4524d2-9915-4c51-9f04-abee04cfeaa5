import { apiClient } from './api';
import { Visit, Episode, PaginatedResponse } from '@/types';

export class VisitService {
  async getVisits(params: { page?: number; limit?: number; filters?: string } = {}): Promise<PaginatedResponse<Visit>> {
    try {
      const { page = 1, limit = 10, filters = '' } = params;
      const response = await apiClient.get(`/api/v1/visit?page=${page}&limit=${limit}${filters}`);
      
      return {
        data: response.visits || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch visits');
    }
  }

  async getVisitById(id: string): Promise<Visit> {
    try {
      const response = await apiClient.get(`/api/v1/visit/${id}`);
      return response.visit;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch visit');
    }
  }

  async createVisit(visitData: Partial<Visit>): Promise<Visit> {
    try {
      const response = await apiClient.post('/api/v1/visit', visitData);
      return response.visit;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to create visit');
    }
  }

  async updateVisit(id: string, data: Partial<Visit>): Promise<Visit> {
    try {
      const response = await apiClient.put(`/api/v1/visit/${id}`, data);
      return response.visit;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update visit');
    }
  }

  async deleteVisit(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/v1/visit?visitId=${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to delete visit');
    }
  }

  async getEpisodes(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<Episode>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/visit/episode?page=${page}&limit=${limit}`);
      
      return {
        data: response.episodes || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch episodes');
    }
  }

  async getAssessments(params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<any>> {
    try {
      const { page = 1, limit = 10 } = params;
      const response = await apiClient.get(`/api/v1/visit/assessment?page=${page}&limit=${limit}`);
      
      return {
        data: response.assessments || [],
        total: response.total || 0,
        page: response.page || page,
        limit: response.limit || limit,
        totalPages: Math.ceil((response.total || 0) / limit),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch assessments');
    }
  }

  async getAssessmentById(id: string): Promise<any> {
    try {
      const response = await apiClient.get(`/api/v1/visit/assessment/${id}`);
      return response.assessment;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to fetch assessment');
    }
  }

  async updateAssessment(id: string, data: any): Promise<any> {
    try {
      const response = await apiClient.put(`/api/v1/visit/assessment/${id}`, data);
      return response.assessment;
    } catch (error: any) {
      throw new Error(error.response?.data?.errorMessage || 'Failed to update assessment');
    }
  }
}

export const visitService = new VisitService();
