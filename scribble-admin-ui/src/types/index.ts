// User and Authentication Types
export interface User {
  _id: string;
  email: string;
  roleId: {
    _id: string;
    name: string;
    permission: string[];
  };
  status: 'active' | 'pending' | 'suspended' | 'inactive' | 'deleted' | 'locked';
  isFirstLogin: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminUser {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  permission: string[];
  lastLoginAt?: string;
  isFirstLogin: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | AdminUser | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  userType: 'super_admin' | 'tenant_admin' | 'clinician' | null;
  tenantId: string | null;
}

export interface LoginRequest {
  email: string;
  password: string;
  tenantId?: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: User | AdminUser;
  userType: 'super_admin' | 'tenant_admin' | 'clinician';
  tenantId?: string;
}

// Tenant Types
export interface Tenant {
  _id: string;
  tenantName: string;
  uniqueName: string;
  databaseName: string;
  subDomain?: string;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTenantRequest {
  tenantName: string;
  uniqueName: string;
  subDomain?: string;
}

// Role Types
export interface Role {
  _id: string;
  name: string;
  permission: string[];
  createdAt: string;
  updatedAt: string;
}

// Visit Types
export interface Visit {
  _id: string;
  clientId: string;
  clinicianId: string;
  episodeId: string;
  visitDate: string;
  status: string;
  discipline: string;
  formType: string;
  createdAt: string;
  updatedAt: string;
}

export interface Episode {
  _id: string;
  clientId: string;
  startDate: string;
  endDate?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

// Form Types
export interface Form {
  _id: string;
  visitId: string;
  formTemplateId: string;
  data: Record<string, any>;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface FormTemplate {
  _id: string;
  name: string;
  version: string;
  fields: FormField[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FormField {
  id: string;
  type: 'text' | 'number' | 'select' | 'checkbox' | 'textarea' | 'date';
  label: string;
  required: boolean;
  options?: string[];
  validation?: Record<string, any>;
}

export interface FormType {
  _id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Discipline {
  _id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Dashboard Types
export interface DashboardKPIs {
  client: number;
  activeClinician: number;
  inActiveClinician: number;
  clinician: number;
  form: number;
  visit: number;
  episode: number;
}

// Settings Types
export interface UserSettings {
  _id: string;
  userId: string;
  preferences: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface GridViewSettings {
  _id: string;
  userId: string;
  gridConfig: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// UI State Types
export interface UIState {
  sidebarOpen: boolean;
  loading: boolean;
  error: string | null;
  success: string | null;
}

// Permission Types
export type Permission = 
  | 'tenant.create' | 'tenant.read' | 'tenant.update' | 'tenant.delete'
  | 'user.create' | 'user.read' | 'user.update' | 'user.delete'
  | 'role.create' | 'role.read' | 'role.update' | 'role.delete'
  | 'visit.create' | 'visit.read' | 'visit.update' | 'visit.delete'
  | 'form.create' | 'form.read' | 'form.update' | 'form.delete'
  | 'assessment.create' | 'assessment.read' | 'assessment.update' | 'assessment.delete'
  | 'admin.create' | 'admin.read' | 'admin.update' | 'admin.delete'
  | 'self.read' | 'self.update'
  | 'grid.read' | 'grid.update';
