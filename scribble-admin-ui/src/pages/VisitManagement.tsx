import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Refresh,
  Assignment,
  Visibility,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchVisits, fetchEpisodes, createVisit, updateVisit, deleteVisit } from '@/store/slices/visitSlice';
import { fetchFormTypes, fetchDisciplines } from '@/store/slices/formSlice';
import { setSuccess, setError } from '@/store/slices/uiSlice';
import { Visit } from '@/types';

interface VisitFormData {
  clientId: string;
  clinicianId: string;
  episodeId: string;
  visitDate: string;
  status: string;
  discipline: string;
  formType: string;
}

function VisitManagement() {
  const dispatch = useAppDispatch();
  const { visits, episodes, isLoading, error, pagination } = useAppSelector((state) => state.visit);
  const { formTypes, disciplines } = useAppSelector((state) => state.form);
  
  const [tabValue, setTabValue] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingVisit, setEditingVisit] = useState<Visit | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [visitToDelete, setVisitToDelete] = useState<Visit | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<VisitFormData>();

  useEffect(() => {
    dispatch(fetchVisits({ page: 1, limit: 10 }));
    dispatch(fetchEpisodes({ page: 1, limit: 100 }));
    dispatch(fetchFormTypes());
    dispatch(fetchDisciplines());
  }, [dispatch]);

  const handleRefresh = () => {
    if (tabValue === 0) {
      dispatch(fetchVisits({ page: pagination.page, limit: pagination.limit }));
    } else {
      dispatch(fetchEpisodes({ page: 1, limit: 100 }));
    }
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    dispatch(fetchVisits({ page: newPage + 1, limit: pagination.limit }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10);
    dispatch(fetchVisits({ page: 1, limit: newLimit }));
  };

  const handleCreateVisit = () => {
    setEditingVisit(null);
    reset();
    setOpenDialog(true);
  };

  const handleEditVisit = (visit: Visit) => {
    setEditingVisit(visit);
    setValue('clientId', visit.clientId);
    setValue('clinicianId', visit.clinicianId);
    setValue('episodeId', visit.episodeId);
    setValue('visitDate', visit.visitDate.split('T')[0]);
    setValue('status', visit.status);
    setValue('discipline', visit.discipline);
    setValue('formType', visit.formType);
    setOpenDialog(true);
  };

  const handleDeleteVisit = (visit: Visit) => {
    setVisitToDelete(visit);
    setDeleteConfirmOpen(true);
  };

  const onSubmit = async (data: VisitFormData) => {
    try {
      if (editingVisit) {
        await dispatch(updateVisit({ id: editingVisit._id, data })).unwrap();
        dispatch(setSuccess('Visit updated successfully'));
      } else {
        await dispatch(createVisit(data)).unwrap();
        dispatch(setSuccess('Visit created successfully'));
      }
      setOpenDialog(false);
      reset();
      handleRefresh();
    } catch (error: any) {
      dispatch(setError(error.message || 'Operation failed'));
    }
  };

  const confirmDelete = async () => {
    if (visitToDelete) {
      try {
        await dispatch(deleteVisit(visitToDelete._id)).unwrap();
        dispatch(setSuccess('Visit deleted successfully'));
        setDeleteConfirmOpen(false);
        setVisitToDelete(null);
        handleRefresh();
      } catch (error: any) {
        dispatch(setError(error.message || 'Delete failed'));
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Visit Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage patient visits and episodes
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={isLoading}>
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateVisit}
          >
            Create Visit
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Visits" />
          <Tab label="Episodes" />
        </Tabs>
      </Box>

      {/* Visits Table */}
      {tabValue === 0 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Visit ID</TableCell>
                  <TableCell>Client ID</TableCell>
                  <TableCell>Clinician ID</TableCell>
                  <TableCell>Visit Date</TableCell>
                  <TableCell>Discipline</TableCell>
                  <TableCell>Form Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : visits.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <Box sx={{ py: 4 }}>
                        <Assignment sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary">
                          No visits found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Create your first visit to get started
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  visits.map((visit) => (
                    <TableRow key={visit._id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="bold">
                          #{visit._id.slice(-6)}
                        </Typography>
                      </TableCell>
                      <TableCell>{visit.clientId}</TableCell>
                      <TableCell>{visit.clinicianId}</TableCell>
                      <TableCell>
                        {new Date(visit.visitDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Chip label={visit.discipline} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Chip label={visit.formType} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={visit.status} 
                          color={getStatusColor(visit.status)} 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={() => handleEditVisit(visit)}
                          color="primary"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="info"
                        >
                          <Visibility />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteVisit(visit)}
                          color="error"
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={pagination.total}
            rowsPerPage={pagination.limit}
            page={pagination.page - 1}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        </Paper>
      )}

      {/* Episodes Table */}
      {tabValue === 1 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Episode ID</TableCell>
                  <TableCell>Client ID</TableCell>
                  <TableCell>Start Date</TableCell>
                  <TableCell>End Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {episodes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography color="text.secondary" py={4}>
                        No episodes found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  episodes.map((episode) => (
                    <TableRow key={episode._id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="bold">
                          #{episode._id.slice(-6)}
                        </Typography>
                      </TableCell>
                      <TableCell>{episode.clientId}</TableCell>
                      <TableCell>
                        {new Date(episode.startDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {episode.endDate ? new Date(episode.endDate).toLocaleDateString() : 'Ongoing'}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={episode.status} 
                          color={episode.status === 'active' ? 'success' : 'default'} 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(episode.createdAt).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Create/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogTitle>
            {editingVisit ? 'Edit Visit' : 'Create New Visit'}
          </DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="Client ID"
              margin="normal"
              {...register('clientId', { required: 'Client ID is required' })}
              error={!!errors.clientId}
              helperText={errors.clientId?.message}
            />
            
            <TextField
              fullWidth
              label="Clinician ID"
              margin="normal"
              {...register('clinicianId', { required: 'Clinician ID is required' })}
              error={!!errors.clinicianId}
              helperText={errors.clinicianId?.message}
            />

            <TextField
              fullWidth
              label="Episode ID"
              margin="normal"
              {...register('episodeId', { required: 'Episode ID is required' })}
              error={!!errors.episodeId}
              helperText={errors.episodeId?.message}
            />

            <TextField
              fullWidth
              label="Visit Date"
              type="date"
              margin="normal"
              InputLabelProps={{ shrink: true }}
              {...register('visitDate', { required: 'Visit date is required' })}
              error={!!errors.visitDate}
              helperText={errors.visitDate?.message}
            />

            <FormControl fullWidth margin="normal">
              <InputLabel>Discipline</InputLabel>
              <Select
                value={watch('discipline') || ''}
                label="Discipline"
                {...register('discipline', { required: 'Discipline is required' })}
                error={!!errors.discipline}
              >
                {disciplines.map((discipline) => (
                  <MenuItem key={discipline._id} value={discipline.name}>
                    {discipline.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal">
              <InputLabel>Form Type</InputLabel>
              <Select
                value={watch('formType') || ''}
                label="Form Type"
                {...register('formType', { required: 'Form type is required' })}
                error={!!errors.formType}
              >
                {formTypes.map((formType) => (
                  <MenuItem key={formType._id} value={formType.name}>
                    {formType.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal">
              <InputLabel>Status</InputLabel>
              <Select
                value={watch('status') || 'scheduled'}
                label="Status"
                {...register('status')}
              >
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="in-progress">In Progress</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
            <Button type="submit" variant="contained" disabled={isLoading}>
              {isLoading ? <CircularProgress size={20} /> : editingVisit ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this visit? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default VisitManagement;
