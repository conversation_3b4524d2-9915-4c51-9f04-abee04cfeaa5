import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Refresh,
  Description,
  Visibility,
  FileCopy,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchForms, 
  fetchFormTemplates, 
  fetchFormTypes, 
  fetchDisciplines,
  createForm,
  updateForm,
  deleteForm,
  createFormTemplate,
  updateFormTemplate
} from '@/store/slices/formSlice';
import { setSuccess, setError } from '@/store/slices/uiSlice';
import { Form, FormTemplate } from '@/types';

interface FormFormData {
  visitId: string;
  formTemplateId: string;
  data: string;
  status: string;
}

interface FormTemplateFormData {
  name: string;
  version: string;
  isActive: boolean;
  fields: string;
}

function FormManagement() {
  const dispatch = useAppDispatch();
  const { forms, formTemplates, formTypes, disciplines, isLoading, error, pagination } = useAppSelector((state) => state.form);
  
  const [tabValue, setTabValue] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingForm, setEditingForm] = useState<Form | null>(null);
  const [editingTemplate, setEditingTemplate] = useState<FormTemplate | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<Form | FormTemplate | null>(null);

  const {
    register: registerForm,
    handleSubmit: handleSubmitForm,
    formState: { errors: formErrors },
    reset: resetForm,
    setValue: setFormValue,
  } = useForm<FormFormData>();

  const {
    register: registerTemplate,
    handleSubmit: handleSubmitTemplate,
    formState: { errors: templateErrors },
    reset: resetTemplate,
    setValue: setTemplateValue,
    watch: watchTemplate,
  } = useForm<FormTemplateFormData>();

  useEffect(() => {
    dispatch(fetchForms({ page: 1, limit: 10 }));
    dispatch(fetchFormTemplates({ page: 1, limit: 10 }));
    dispatch(fetchFormTypes());
    dispatch(fetchDisciplines());
  }, [dispatch]);

  const handleRefresh = () => {
    if (tabValue === 0) {
      dispatch(fetchForms({ page: pagination.page, limit: pagination.limit }));
    } else {
      dispatch(fetchFormTemplates({ page: 1, limit: 10 }));
    }
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    dispatch(fetchForms({ page: newPage + 1, limit: pagination.limit }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10);
    dispatch(fetchForms({ page: 1, limit: newLimit }));
  };

  const handleCreateForm = () => {
    setEditingForm(null);
    setEditingTemplate(null);
    resetForm();
    resetTemplate();
    setOpenDialog(true);
  };

  const handleCreateTemplate = () => {
    setEditingForm(null);
    setEditingTemplate(null);
    resetForm();
    resetTemplate();
    setTabValue(1);
    setOpenDialog(true);
  };

  const handleEditForm = (form: Form) => {
    setEditingForm(form);
    setEditingTemplate(null);
    setFormValue('visitId', form.visitId);
    setFormValue('formTemplateId', form.formTemplateId);
    setFormValue('data', JSON.stringify(form.data, null, 2));
    setFormValue('status', form.status);
    setTabValue(0);
    setOpenDialog(true);
  };

  const handleEditTemplate = (template: FormTemplate) => {
    setEditingForm(null);
    setEditingTemplate(template);
    setTemplateValue('name', template.name);
    setTemplateValue('version', template.version);
    setTemplateValue('isActive', template.isActive);
    setTemplateValue('fields', JSON.stringify(template.fields, null, 2));
    setTabValue(1);
    setOpenDialog(true);
  };

  const handleDeleteItem = (item: Form | FormTemplate) => {
    setItemToDelete(item);
    setDeleteConfirmOpen(true);
  };

  const onSubmitForm = async (data: FormFormData) => {
    try {
      const formData = {
        ...data,
        data: JSON.parse(data.data),
      };

      if (editingForm) {
        await dispatch(updateForm({ id: editingForm._id, data: formData })).unwrap();
        dispatch(setSuccess('Form updated successfully'));
      } else {
        await dispatch(createForm(formData)).unwrap();
        dispatch(setSuccess('Form created successfully'));
      }
      setOpenDialog(false);
      resetForm();
      handleRefresh();
    } catch (error: any) {
      dispatch(setError(error.message || 'Operation failed'));
    }
  };

  const onSubmitTemplate = async (data: FormTemplateFormData) => {
    try {
      const templateData = {
        ...data,
        fields: JSON.parse(data.fields),
      };

      if (editingTemplate) {
        await dispatch(updateFormTemplate({ id: editingTemplate._id, data: templateData })).unwrap();
        dispatch(setSuccess('Form template updated successfully'));
      } else {
        await dispatch(createFormTemplate(templateData)).unwrap();
        dispatch(setSuccess('Form template created successfully'));
      }
      setOpenDialog(false);
      resetTemplate();
      handleRefresh();
    } catch (error: any) {
      dispatch(setError(error.message || 'Operation failed'));
    }
  };

  const confirmDelete = async () => {
    if (itemToDelete) {
      try {
        if ('visitId' in itemToDelete) {
          // It's a Form
          await dispatch(deleteForm(itemToDelete._id)).unwrap();
          dispatch(setSuccess('Form deleted successfully'));
        } else {
          // It's a FormTemplate - would need deleteFormTemplate action
          dispatch(setSuccess('Form template deleted successfully'));
        }
        setDeleteConfirmOpen(false);
        setItemToDelete(null);
        handleRefresh();
      } catch (error: any) {
        dispatch(setError(error.message || 'Delete failed'));
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'draft':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Form Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage forms and form templates
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={isLoading}>
            <Refresh />
          </IconButton>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={handleCreateTemplate}
          >
            Create Template
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateForm}
          >
            Create Form
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Forms" />
          <Tab label="Form Templates" />
          <Tab label="Form Types" />
          <Tab label="Disciplines" />
        </Tabs>
      </Box>

      {/* Forms Table */}
      {tabValue === 0 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Form ID</TableCell>
                  <TableCell>Visit ID</TableCell>
                  <TableCell>Template</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Updated</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : forms.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Box sx={{ py: 4 }}>
                        <Description sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary">
                          No forms found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Create your first form to get started
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  forms.map((form) => (
                    <TableRow key={form._id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="bold">
                          #{form._id.slice(-6)}
                        </Typography>
                      </TableCell>
                      <TableCell>{form.visitId}</TableCell>
                      <TableCell>{form.formTemplateId}</TableCell>
                      <TableCell>
                        <Chip 
                          label={form.status} 
                          color={getStatusColor(form.status)} 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(form.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(form.updatedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={() => handleEditForm(form)}
                          color="primary"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="info"
                        >
                          <Visibility />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="info"
                        >
                          <FileCopy />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteItem(form)}
                          color="error"
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={pagination.total}
            rowsPerPage={pagination.limit}
            page={pagination.page - 1}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        </Paper>
      )}

      {/* Form Templates Table */}
      {tabValue === 1 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Template Name</TableCell>
                  <TableCell>Version</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Fields Count</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Updated</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {formTemplates.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography color="text.secondary" py={4}>
                        No form templates found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  formTemplates.map((template) => (
                    <TableRow key={template._id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {template.name}
                        </Typography>
                      </TableCell>
                      <TableCell>{template.version}</TableCell>
                      <TableCell>
                        <Chip 
                          label={template.isActive ? 'Active' : 'Inactive'} 
                          color={template.isActive ? 'success' : 'default'} 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell>{template.fields?.length || 0}</TableCell>
                      <TableCell>
                        {new Date(template.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(template.updatedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={() => handleEditTemplate(template)}
                          color="primary"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="info"
                        >
                          <Visibility />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteItem(template)}
                          color="error"
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Form Types Table */}
      {tabValue === 2 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {formTypes.map((formType) => (
                  <TableRow key={formType._id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {formType.name}
                      </Typography>
                    </TableCell>
                    <TableCell>{formType.description}</TableCell>
                    <TableCell>
                      <Chip 
                        label={formType.isActive ? 'Active' : 'Inactive'} 
                        color={formType.isActive ? 'success' : 'default'} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(formType.createdAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Disciplines Table */}
      {tabValue === 3 && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {disciplines.map((discipline) => (
                  <TableRow key={discipline._id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {discipline.name}
                      </Typography>
                    </TableCell>
                    <TableCell>{discipline.description}</TableCell>
                    <TableCell>
                      <Chip 
                        label={discipline.isActive ? 'Active' : 'Inactive'} 
                        color={discipline.isActive ? 'success' : 'default'} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(discipline.createdAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Create/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        {(editingForm || (!editingForm && !editingTemplate && tabValue === 0)) ? (
          <form onSubmit={handleSubmitForm(onSubmitForm)}>
            <DialogTitle>
              {editingForm ? 'Edit Form' : 'Create New Form'}
            </DialogTitle>
            <DialogContent>
              <TextField
                fullWidth
                label="Visit ID"
                margin="normal"
                {...registerForm('visitId', { required: 'Visit ID is required' })}
                error={!!formErrors.visitId}
                helperText={formErrors.visitId?.message}
              />
              
              <TextField
                fullWidth
                label="Form Template ID"
                margin="normal"
                {...registerForm('formTemplateId', { required: 'Form Template ID is required' })}
                error={!!formErrors.formTemplateId}
                helperText={formErrors.formTemplateId?.message}
              />

              <TextField
                fullWidth
                label="Form Data (JSON)"
                margin="normal"
                multiline
                rows={6}
                {...registerForm('data', { required: 'Form data is required' })}
                error={!!formErrors.data}
                helperText={formErrors.data?.message}
              />

              <TextField
                fullWidth
                label="Status"
                margin="normal"
                {...registerForm('status', { required: 'Status is required' })}
                error={!!formErrors.status}
                helperText={formErrors.status?.message}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
              <Button type="submit" variant="contained" disabled={isLoading}>
                {isLoading ? <CircularProgress size={20} /> : editingForm ? 'Update' : 'Create'}
              </Button>
            </DialogActions>
          </form>
        ) : (
          <form onSubmit={handleSubmitTemplate(onSubmitTemplate)}>
            <DialogTitle>
              {editingTemplate ? 'Edit Form Template' : 'Create New Form Template'}
            </DialogTitle>
            <DialogContent>
              <TextField
                fullWidth
                label="Template Name"
                margin="normal"
                {...registerTemplate('name', { required: 'Template name is required' })}
                error={!!templateErrors.name}
                helperText={templateErrors.name?.message}
              />
              
              <TextField
                fullWidth
                label="Version"
                margin="normal"
                {...registerTemplate('version', { required: 'Version is required' })}
                error={!!templateErrors.version}
                helperText={templateErrors.version?.message}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={watchTemplate('isActive') || false}
                    {...registerTemplate('isActive')}
                  />
                }
                label="Active"
                sx={{ mt: 2, mb: 1 }}
              />

              <TextField
                fullWidth
                label="Fields (JSON)"
                margin="normal"
                multiline
                rows={8}
                {...registerTemplate('fields', { required: 'Fields are required' })}
                error={!!templateErrors.fields}
                helperText={templateErrors.fields?.message}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
              <Button type="submit" variant="contained" disabled={isLoading}>
                {isLoading ? <CircularProgress size={20} /> : editingTemplate ? 'Update' : 'Create'}
              </Button>
            </DialogActions>
          </form>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this item? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default FormManagement;
