import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Paper,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  People,
  Assignment,
  Description,
  Refresh,
  Add,
  TrendingUp,
  Assessment,
  PersonAdd,
} from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchDashboardKPIs } from '@/store/slices/dashboardSlice';
import { fetchUsers } from '@/store/slices/userSlice';
import { fetchVisits } from '@/store/slices/visitSlice';
import { useNavigate } from 'react-router-dom';

function TenantAdminDashboard() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { kpis, isLoading: dashboardLoading } = useAppSelector((state) => state.dashboard);
  const { users } = useAppSelector((state) => state.user);
  const { visits } = useAppSelector((state) => state.visit);
  const { user, tenantId } = useAppSelector((state) => state.auth);

  useEffect(() => {
    dispatch(fetchDashboardKPIs());
    dispatch(fetchUsers({ page: 1, limit: 5 }));
    dispatch(fetchVisits({ page: 1, limit: 5 }));
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchDashboardKPIs());
    dispatch(fetchUsers({ page: 1, limit: 5 }));
    dispatch(fetchVisits({ page: 1, limit: 5 }));
  };

  const handleCreateUser = () => {
    navigate('/users');
  };

  const handleManageUsers = () => {
    navigate('/users');
  };

  const handleManageVisits = () => {
    navigate('/visits');
  };

  const handleManageForms = () => {
    navigate('/forms');
  };

  const recentUsers = users.slice(0, 5);
  const recentVisits = visits.slice(0, 5);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome back, {user?.email}
          </Typography>
          {tenantId && (
            <Chip label={`Tenant: ${tenantId}`} color="primary" size="small" sx={{ mt: 1 }} />
          )}
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={dashboardLoading}>
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={handleCreateUser}
          >
            Add User
          </Button>
        </Box>
      </Box>

      {/* Loading Bar */}
      {dashboardLoading && <LinearProgress sx={{ mb: 2 }} />}

      {/* KPI Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Clients
                  </Typography>
                  <Typography variant="h4">
                    {kpis?.client || 0}
                  </Typography>
                </Box>
                <People color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Clinicians
                  </Typography>
                  <Typography variant="h4">
                    {kpis?.activeClinician || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    of {kpis?.clinician || 0} total
                  </Typography>
                </Box>
                <TrendingUp color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Visits
                  </Typography>
                  <Typography variant="h4">
                    {kpis?.visit || 0}
                  </Typography>
                </Box>
                <Assignment color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Forms
                  </Typography>
                  <Typography variant="h4">
                    {kpis?.form || 0}
                  </Typography>
                </Box>
                <Description color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Users */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Recent Users</Typography>
              <Button size="small" onClick={handleManageUsers}>
                View All
              </Button>
            </Box>
            
            {recentUsers.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {recentUsers.map((user) => (
                  <Box
                    key={user._id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {user.email}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {user.roleId?.name} • Created {new Date(user.createdAt).toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Chip 
                      label={user.status} 
                      color={user.status === 'active' ? 'success' : 'default'} 
                      size="small" 
                    />
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary" textAlign="center" py={4}>
                No users found. Create your first user to get started.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Recent Visits */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Recent Visits</Typography>
              <Button size="small" onClick={handleManageVisits}>
                View All
              </Button>
            </Box>
            
            {recentVisits.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {recentVisits.map((visit) => (
                  <Box
                    key={visit._id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle1" fontWeight="bold">
                        Visit #{visit._id.slice(-6)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {visit.discipline} • {new Date(visit.visitDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Chip 
                      label={visit.status} 
                      color="primary" 
                      size="small" 
                    />
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary" textAlign="center" py={4}>
                No visits found.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<PersonAdd />}
                  onClick={handleCreateUser}
                  fullWidth
                  size="large"
                >
                  Add User
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<People />}
                  onClick={handleManageUsers}
                  fullWidth
                  size="large"
                >
                  Manage Users
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<Assignment />}
                  onClick={handleManageVisits}
                  fullWidth
                  size="large"
                >
                  Manage Visits
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  startIcon={<Description />}
                  onClick={handleManageForms}
                  fullWidth
                  size="large"
                >
                  Manage Forms
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default TenantAdminDashboard;
