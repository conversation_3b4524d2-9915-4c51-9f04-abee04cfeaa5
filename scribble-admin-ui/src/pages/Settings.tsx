import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Text<PERSON>ield,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  <PERSON>ert,
  Grid,
  Card,
  CardContent,
  CardActions,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Security,
  Notifications,
  Palette,
  Save,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAppSelector, useAppDispatch } from '@/store';
import { setSuccess, setError } from '@/store/slices/uiSlice';

interface GeneralSettingsData {
  organizationName: string;
  timezone: string;
  language: string;
  dateFormat: string;
}

interface SecuritySettingsData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  enableMFA: boolean;
  sessionTimeout: number;
}

interface NotificationSettingsData {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  weeklyReports: boolean;
  systemAlerts: boolean;
}

function Settings() {
  const dispatch = useAppDispatch();
  const { user, userType } = useAppSelector((state) => state.auth);
  const [tabValue, setTabValue] = useState(0);

  const {
    register: registerGeneral,
    handleSubmit: handleSubmitGeneral,
    formState: { errors: generalErrors },
  } = useForm<GeneralSettingsData>();

  const {
    register: registerSecurity,
    handleSubmit: handleSubmitSecurity,
    formState: { errors: securityErrors },
    watch: watchSecurity,
  } = useForm<SecuritySettingsData>();

  const {
    register: registerNotifications,
    handleSubmit: handleSubmitNotifications,
    watch: watchNotifications,
  } = useForm<NotificationSettingsData>();

  const onSubmitGeneral = async (data: GeneralSettingsData) => {
    try {
      // TODO: Implement general settings update
      console.log('General settings:', data);
      dispatch(setSuccess('General settings updated successfully'));
    } catch (error: any) {
      dispatch(setError('Failed to update general settings'));
    }
  };

  const onSubmitSecurity = async (data: SecuritySettingsData) => {
    try {
      if (data.newPassword !== data.confirmPassword) {
        dispatch(setError('Passwords do not match'));
        return;
      }
      // TODO: Implement security settings update
      console.log('Security settings:', data);
      dispatch(setSuccess('Security settings updated successfully'));
    } catch (error: any) {
      dispatch(setError('Failed to update security settings'));
    }
  };

  const onSubmitNotifications = async (data: NotificationSettingsData) => {
    try {
      // TODO: Implement notification settings update
      console.log('Notification settings:', data);
      dispatch(setSuccess('Notification settings updated successfully'));
    } catch (error: any) {
      dispatch(setError('Failed to update notification settings'));
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your application settings and preferences
        </Typography>
      </Box>

      <Paper>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab icon={<SettingsIcon />} label="General" />
            <Tab icon={<Security />} label="Security" />
            <Tab icon={<Notifications />} label="Notifications" />
            <Tab icon={<Palette />} label="Appearance" />
          </Tabs>
        </Box>

        {/* General Settings */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <form onSubmit={handleSubmitGeneral(onSubmitGeneral)}>
              <Typography variant="h6" gutterBottom>
                General Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Organization Name"
                    margin="normal"
                    defaultValue="Scribble Healthcare"
                    {...registerGeneral('organizationName')}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Timezone"
                    margin="normal"
                    defaultValue="UTC"
                    {...registerGeneral('timezone')}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Language"
                    margin="normal"
                    defaultValue="English"
                    {...registerGeneral('language')}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Date Format"
                    margin="normal"
                    defaultValue="MM/DD/YYYY"
                    {...registerGeneral('dateFormat')}
                  />
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<Save />}
                >
                  Save General Settings
                </Button>
              </Box>
            </form>
          </Box>
        )}

        {/* Security Settings */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Security Settings
            </Typography>

            <form onSubmit={handleSubmitSecurity(onSubmitSecurity)}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Change Password
                  </Typography>
                  
                  <TextField
                    fullWidth
                    label="Current Password"
                    type="password"
                    margin="normal"
                    {...registerSecurity('currentPassword', { required: 'Current password is required' })}
                    error={!!securityErrors.currentPassword}
                    helperText={securityErrors.currentPassword?.message}
                  />
                  
                  <TextField
                    fullWidth
                    label="New Password"
                    type="password"
                    margin="normal"
                    {...registerSecurity('newPassword', {
                      required: 'New password is required',
                      minLength: { value: 6, message: 'Password must be at least 6 characters' }
                    })}
                    error={!!securityErrors.newPassword}
                    helperText={securityErrors.newPassword?.message}
                  />
                  
                  <TextField
                    fullWidth
                    label="Confirm New Password"
                    type="password"
                    margin="normal"
                    {...registerSecurity('confirmPassword', { required: 'Please confirm your password' })}
                    error={!!securityErrors.confirmPassword}
                    helperText={securityErrors.confirmPassword?.message}
                  />
                </CardContent>
              </Card>

              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Multi-Factor Authentication
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        defaultChecked={false}
                        {...registerSecurity('enableMFA')}
                      />
                    }
                    label="Enable Multi-Factor Authentication"
                  />
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Add an extra layer of security to your account
                  </Typography>
                </CardContent>
              </Card>

              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Session Management
                  </Typography>
                  
                  <TextField
                    fullWidth
                    label="Session Timeout (minutes)"
                    type="number"
                    margin="normal"
                    defaultValue={30}
                    {...registerSecurity('sessionTimeout')}
                  />
                </CardContent>
              </Card>

              <Button
                type="submit"
                variant="contained"
                startIcon={<Save />}
              >
                Save Security Settings
              </Button>
            </form>
          </Box>
        )}

        {/* Notification Settings */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Notification Settings
            </Typography>

            <form onSubmit={handleSubmitNotifications(onSubmitNotifications)}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Communication Preferences
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        defaultChecked={true}
                        {...registerNotifications('emailNotifications')}
                      />
                    }
                    label="Email Notifications"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        defaultChecked={false}
                        {...registerNotifications('pushNotifications')}
                      />
                    }
                    label="Push Notifications"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        defaultChecked={false}
                        {...registerNotifications('smsNotifications')}
                      />
                    }
                    label="SMS Notifications"
                  />
                </CardContent>
              </Card>

              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Report Preferences
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        defaultChecked={true}
                        {...registerNotifications('weeklyReports')}
                      />
                    }
                    label="Weekly Reports"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        defaultChecked={true}
                        {...registerNotifications('systemAlerts')}
                      />
                    }
                    label="System Alerts"
                  />
                </CardContent>
              </Card>

              <Button
                type="submit"
                variant="contained"
                startIcon={<Save />}
              >
                Save Notification Settings
              </Button>
            </form>
          </Box>
        )}

        {/* Appearance Settings */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Appearance Settings
            </Typography>

            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Theme Preferences
                </Typography>
                
                <Alert severity="info" sx={{ mb: 2 }}>
                  Theme customization will be available in a future update.
                </Alert>
                
                <FormControlLabel
                  control={<Switch defaultChecked={false} disabled />}
                  label="Dark Mode"
                />
                
                <FormControlLabel
                  control={<Switch defaultChecked={true} disabled />}
                  label="Compact Layout"
                />
              </CardContent>
            </Card>
          </Box>
        )}
      </Paper>
    </Box>
  );
}

export default Settings;
