import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Paper,
  Chip,
} from '@mui/material';
import {
  Business,
  People,
  Refresh,
  Add,
  TrendingUp,
  Assessment,
} from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchTenants } from '@/store/slices/tenantSlice';
import { useNavigate } from 'react-router-dom';

function SuperAdminDashboard() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { tenants, isLoading } = useAppSelector((state) => state.tenant);
  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    dispatch(fetchTenants({ page: 1, limit: 100 }));
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchTenants({ page: 1, limit: 100 }));
  };

  const handleCreateTenant = () => {
    navigate('/tenants');
  };

  const handleManageTenants = () => {
    navigate('/tenants');
  };

  const activeTenants = tenants.filter(tenant => tenant.tenantName);
  const recentTenants = tenants.slice(0, 5);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Super Admin Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome back, {user && 'firstName' in user ? `${user.firstName} ${user.lastName}` : user?.email}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={isLoading}>
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateTenant}
          >
            Create Tenant
          </Button>
        </Box>
      </Box>

      {/* KPI Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Tenants
                  </Typography>
                  <Typography variant="h4">
                    {tenants.length}
                  </Typography>
                </Box>
                <Business color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Tenants
                  </Typography>
                  <Typography variant="h4">
                    {activeTenants.length}
                  </Typography>
                </Box>
                <TrendingUp color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    System Health
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    Good
                  </Typography>
                </Box>
                <Assessment color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Platform Status
                  </Typography>
                  <Chip label="Online" color="success" size="small" />
                </Box>
                <People color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Tenants */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Recent Tenants</Typography>
              <Button size="small" onClick={handleManageTenants}>
                View All
              </Button>
            </Box>
            
            {recentTenants.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {recentTenants.map((tenant) => (
                  <Box
                    key={tenant._id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {tenant.tenantName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {tenant.uniqueName} • Created {new Date(tenant.createdAt).toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {tenant.subDomain && (
                        <Chip label={tenant.subDomain} size="small" variant="outlined" />
                      )}
                      <Chip label="Active" color="success" size="small" />
                    </Box>
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary" textAlign="center" py={4}>
                No tenants found. Create your first tenant to get started.
              </Typography>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Add />}
                onClick={handleCreateTenant}
                fullWidth
              >
                Create New Tenant
              </Button>
              <Button
                variant="outlined"
                startIcon={<Business />}
                onClick={handleManageTenants}
                fullWidth
              >
                Manage Tenants
              </Button>
              <Button
                variant="outlined"
                startIcon={<Assessment />}
                fullWidth
              >
                System Reports
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default SuperAdminDashboard;
