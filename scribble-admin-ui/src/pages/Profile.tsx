import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Avatar,
  Card,
  CardContent,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
} from '@mui/material';
import {
  Person,
  Email,
  Badge,
  CalendarToday,
  Security,
  Save,
  Edit,
  History,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAppSelector, useAppDispatch } from '@/store';
import { updateProfile } from '@/store/slices/authSlice';
import { setSuccess, setError } from '@/store/slices/uiSlice';

interface ProfileFormData {
  firstName?: string;
  lastName?: string;
  email: string;
}

function Profile() {
  const dispatch = useAppDispatch();
  const { user, userType, tenantId } = useAppSelector((state) => state.auth);
  const [isEditing, setIsEditing] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProfileFormData>({
    defaultValues: {
      firstName: userType === 'super_admin' && 'firstName' in user! ? user.firstName : '',
      lastName: userType === 'super_admin' && 'lastName' in user! ? user.lastName : '',
      email: user?.email || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    try {
      await dispatch(updateProfile(data)).unwrap();
      dispatch(setSuccess('Profile updated successfully'));
      setIsEditing(false);
    } catch (error: any) {
      dispatch(setError(error.message || 'Failed to update profile'));
    }
  };

  const handleEditToggle = () => {
    if (isEditing) {
      reset();
    }
    setIsEditing(!isEditing);
  };

  const getUserDisplayName = () => {
    if (userType === 'super_admin' && 'firstName' in user!) {
      return `${user.firstName} ${user.lastName}`;
    }
    return user?.email || 'User';
  };

  const getUserTypeLabel = () => {
    switch (userType) {
      case 'super_admin':
        return 'Super Administrator';
      case 'tenant_admin':
        return 'Tenant Administrator';
      case 'clinician':
        return 'Clinician';
      default:
        return 'User';
    }
  };

  const getUserTypeColor = () => {
    switch (userType) {
      case 'super_admin':
        return 'error';
      case 'tenant_admin':
        return 'primary';
      case 'clinician':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getPermissions = () => {
    if (userType === 'super_admin' && 'permission' in user!) {
      return user.permission;
    }
    if (userType !== 'super_admin' && user && 'roleId' in user) {
      return user.roleId?.permission || [];
    }
    return [];
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Profile
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your personal information and account settings
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Information */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">Personal Information</Typography>
              <Button
                variant={isEditing ? 'outlined' : 'contained'}
                startIcon={<Edit />}
                onClick={handleEditToggle}
              >
                {isEditing ? 'Cancel' : 'Edit Profile'}
              </Button>
            </Box>

            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid container spacing={3}>
                {userType === 'super_admin' && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="First Name"
                        disabled={!isEditing}
                        {...register('firstName')}
                        error={!!errors.firstName}
                        helperText={errors.firstName?.message}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Last Name"
                        disabled={!isEditing}
                        {...register('lastName')}
                        error={!!errors.lastName}
                        helperText={errors.lastName?.message}
                      />
                    </Grid>
                  </>
                )}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    disabled={!isEditing}
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Invalid email address',
                      },
                    })}
                    error={!!errors.email}
                    helperText={errors.email?.message}
                  />
                </Grid>
              </Grid>

              {isEditing && (
                <Box sx={{ mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<Save />}
                  >
                    Save Changes
                  </Button>
                </Box>
              )}
            </form>
          </Paper>
        </Grid>

        {/* Profile Summary */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                }}
              >
                {getUserDisplayName().charAt(0).toUpperCase()}
              </Avatar>
              
              <Typography variant="h6" gutterBottom>
                {getUserDisplayName()}
              </Typography>
              
              <Chip
                label={getUserTypeLabel()}
                color={getUserTypeColor()}
                sx={{ mb: 2 }}
              />
              
              {tenantId && (
                <Chip
                  label={`Tenant: ${tenantId}`}
                  variant="outlined"
                  size="small"
                />
              )}
            </CardContent>
          </Card>

          {/* Account Details */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Account Details
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Email />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary={user?.email}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <Badge />
                  </ListItemIcon>
                  <ListItemText
                    primary="Role"
                    secondary={getUserTypeLabel()}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CalendarToday />
                  </ListItemIcon>
                  <ListItemText
                    primary="Member Since"
                    secondary={user ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                  />
                </ListItem>
                
                {user && 'lastLoginAt' in user && user.lastLoginAt && (
                  <ListItem>
                    <ListItemIcon>
                      <History />
                    </ListItemIcon>
                    <ListItemText
                      primary="Last Login"
                      secondary={new Date(user.lastLoginAt).toLocaleDateString()}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Permissions */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Permissions
            </Typography>
            
            {getPermissions().length > 0 ? (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {getPermissions().map((permission) => (
                  <Chip
                    key={permission}
                    label={permission}
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Box>
            ) : (
              <Alert severity="info">
                No specific permissions assigned to this account.
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Security Information */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Security
            </Typography>
            
            <List>
              <ListItem>
                <ListItemIcon>
                  <Security />
                </ListItemIcon>
                <ListItemText
                  primary="Password"
                  secondary="Last changed: Never"
                />
                <Button variant="outlined" size="small">
                  Change Password
                </Button>
              </ListItem>
              
              <Divider />
              
              <ListItem>
                <ListItemIcon>
                  <Security />
                </ListItemIcon>
                <ListItemText
                  primary="Two-Factor Authentication"
                  secondary="Not enabled"
                />
                <Button variant="outlined" size="small">
                  Enable 2FA
                </Button>
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Profile;
