import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Refresh,
  Business,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchTenants, createTenant, updateTenant, deleteTenant } from '@/store/slices/tenantSlice';
import { setSuccess, setError } from '@/store/slices/uiSlice';
import { CreateTenantRequest, Tenant } from '@/types';

interface TenantFormData {
  tenantName: string;
  uniqueName: string;
  subDomain?: string;
}

function TenantManagement() {
  const dispatch = useAppDispatch();
  const { tenants, isLoading, error, pagination } = useAppSelector((state) => state.tenant);
  
  const [openDialog, setOpenDialog] = useState(false);
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [tenantToDelete, setTenantToDelete] = useState<Tenant | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<TenantFormData>();

  useEffect(() => {
    dispatch(fetchTenants({ page: 1, limit: 10 }));
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchTenants({ page: pagination.page, limit: pagination.limit }));
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    dispatch(fetchTenants({ page: newPage + 1, limit: pagination.limit }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10);
    dispatch(fetchTenants({ page: 1, limit: newLimit }));
  };

  const handleCreateTenant = () => {
    setEditingTenant(null);
    reset();
    setOpenDialog(true);
  };

  const handleEditTenant = (tenant: Tenant) => {
    setEditingTenant(tenant);
    setValue('tenantName', tenant.tenantName);
    setValue('uniqueName', tenant.uniqueName);
    setValue('subDomain', tenant.subDomain || '');
    setOpenDialog(true);
  };

  const handleDeleteTenant = (tenant: Tenant) => {
    setTenantToDelete(tenant);
    setDeleteConfirmOpen(true);
  };

  const onSubmit = async (data: TenantFormData) => {
    try {
      if (editingTenant) {
        await dispatch(updateTenant({ id: editingTenant._id, data })).unwrap();
        dispatch(setSuccess('Tenant updated successfully'));
      } else {
        await dispatch(createTenant(data as CreateTenantRequest)).unwrap();
        dispatch(setSuccess('Tenant created successfully'));
      }
      setOpenDialog(false);
      reset();
      handleRefresh();
    } catch (error: any) {
      dispatch(setError(error.message || 'Operation failed'));
    }
  };

  const confirmDelete = async () => {
    if (tenantToDelete) {
      try {
        await dispatch(deleteTenant(tenantToDelete._id)).unwrap();
        dispatch(setSuccess('Tenant deleted successfully'));
        setDeleteConfirmOpen(false);
        setTenantToDelete(null);
        handleRefresh();
      } catch (error: any) {
        dispatch(setError(error.message || 'Delete failed'));
      }
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Tenant Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage all tenants in the system
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={isLoading}>
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateTenant}
          >
            Create Tenant
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tenants Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Tenant Name</TableCell>
                <TableCell>Unique Name</TableCell>
                <TableCell>Subdomain</TableCell>
                <TableCell>Database</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : tenants.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Box sx={{ py: 4 }}>
                      <Business sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        No tenants found
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Create your first tenant to get started
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                tenants.map((tenant) => (
                  <TableRow key={tenant._id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {tenant.tenantName}
                      </Typography>
                    </TableCell>
                    <TableCell>{tenant.uniqueName}</TableCell>
                    <TableCell>
                      {tenant.subDomain ? (
                        <Chip label={tenant.subDomain} size="small" variant="outlined" />
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {tenant.databaseName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {new Date(tenant.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Chip label="Active" color="success" size="small" />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={() => handleEditTenant(tenant)}
                        color="primary"
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteTenant(tenant)}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={pagination.total}
          rowsPerPage={pagination.limit}
          page={pagination.page - 1}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      </Paper>

      {/* Create/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogTitle>
            {editingTenant ? 'Edit Tenant' : 'Create New Tenant'}
          </DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="Tenant Name"
              margin="normal"
              {...register('tenantName', { required: 'Tenant name is required' })}
              error={!!errors.tenantName}
              helperText={errors.tenantName?.message}
            />
            <TextField
              fullWidth
              label="Unique Name"
              margin="normal"
              {...register('uniqueName', { required: 'Unique name is required' })}
              error={!!errors.uniqueName}
              helperText={errors.uniqueName?.message}
            />
            <TextField
              fullWidth
              label="Subdomain (Optional)"
              margin="normal"
              {...register('subDomain')}
              error={!!errors.subDomain}
              helperText={errors.subDomain?.message}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
            <Button type="submit" variant="contained" disabled={isLoading}>
              {isLoading ? <CircularProgress size={20} /> : editingTenant ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete tenant "{tenantToDelete?.tenantName}"? 
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default TenantManagement;
