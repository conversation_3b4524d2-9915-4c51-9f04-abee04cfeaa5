import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
  Link,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { login, clearError } from '@/store/slices/authSlice';
import { LoginRequest } from '@/types';

interface LoginFormData {
  email: string;
  password: string;
  tenantId?: string;
}

function LoginPage() {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);
  const [loginType, setLoginType] = useState<'super_admin' | 'tenant'>('super_admin');
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginFormData>();

  const onSubmit = async (data: LoginFormData) => {
    dispatch(clearError());
    
    const loginData: LoginRequest = {
      email: data.email,
      password: data.password,
    };

    if (loginType === 'tenant' && data.tenantId) {
      loginData.tenantId = data.tenantId;
    }

    try {
      await dispatch(login(loginData)).unwrap();
    } catch (error) {
      // Error is handled by the slice
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: 'super_admin' | 'tenant') => {
    setLoginType(newValue);
    reset();
    dispatch(clearError());
  };

  const handleForgotPassword = () => {
    setShowForgotPassword(true);
    // TODO: Implement forgot password functionality
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: 2,
      }}
    >
      <Card sx={{ maxWidth: 400, width: '100%' }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Scribble Admin
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Sign in to manage your platform
            </Typography>
          </Box>

          <Tabs
            value={loginType}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ mb: 3 }}
          >
            <Tab label="Super Admin" value="super_admin" />
            <Tab label="Tenant Admin" value="tenant" />
          </Tabs>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              margin="normal"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
              error={!!errors.email}
              helperText={errors.email?.message}
            />

            <TextField
              fullWidth
              label="Password"
              type="password"
              margin="normal"
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 6,
                  message: 'Password must be at least 6 characters',
                },
              })}
              error={!!errors.password}
              helperText={errors.password?.message}
            />

            {loginType === 'tenant' && (
              <TextField
                fullWidth
                label="Tenant ID"
                margin="normal"
                placeholder="Enter your tenant identifier"
                {...register('tenantId', {
                  required: loginType === 'tenant' ? 'Tenant ID is required' : false,
                })}
                error={!!errors.tenantId}
                helperText={errors.tenantId?.message}
              />
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading}
              sx={{ mt: 3, mb: 2 }}
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Sign In'
              )}
            </Button>

            <Box sx={{ textAlign: 'center' }}>
              <Link
                component="button"
                type="button"
                variant="body2"
                onClick={handleForgotPassword}
                sx={{ textDecoration: 'none' }}
              >
                Forgot your password?
              </Link>
            </Box>
          </form>

          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="caption" display="block" gutterBottom>
              <strong>Demo Credentials:</strong>
            </Typography>
            <Typography variant="caption" display="block">
              Super Admin: <EMAIL> / Admin@123
            </Typography>
            <Typography variant="caption" display="block">
              Tenant Admin: Use your tenant-specific credentials
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}

export default LoginPage;
