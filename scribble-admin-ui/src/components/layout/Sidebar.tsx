import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Tool<PERSON>,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard,
  Business,
  People,
  Assignment,
  Description,
  Settings,
  AccountCircle,
  Bar<PERSON>hart,
  SupervisorAccount,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store';

interface SidebarProps {
  onClose?: () => void;
}

interface NavigationItem {
  text: string;
  icon: React.ReactElement;
  path: string;
  permissions?: string[];
  userTypes?: ('super_admin' | 'tenant_admin' | 'clinician')[];
}

function Sidebar({ onClose }: SidebarProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { userType, user } = useAppSelector((state) => state.auth);

  const navigationItems: NavigationItem[] = [
    {
      text: 'Dashboard',
      icon: <Dashboard />,
      path: '/dashboard',
      userTypes: ['super_admin', 'tenant_admin', 'clinician'],
    },
    {
      text: 'Tenant Management',
      icon: <Business />,
      path: '/tenants',
      userTypes: ['super_admin'],
      permissions: ['tenant.create', 'tenant.read'],
    },
    {
      text: 'User Management',
      icon: <People />,
      path: '/users',
      userTypes: ['tenant_admin'],
      permissions: ['user.create', 'user.read'],
    },
    {
      text: 'Visit Management',
      icon: <Assignment />,
      path: '/visits',
      userTypes: ['tenant_admin', 'clinician'],
      permissions: ['visit.read'],
    },
    {
      text: 'Form Management',
      icon: <Description />,
      path: '/forms',
      userTypes: ['tenant_admin', 'clinician'],
      permissions: ['form.read'],
    },
    {
      text: 'Analytics',
      icon: <BarChart />,
      path: '/analytics',
      userTypes: ['super_admin', 'tenant_admin'],
    },
  ];

  const commonItems: NavigationItem[] = [
    {
      text: 'Profile',
      icon: <AccountCircle />,
      path: '/profile',
      userTypes: ['super_admin', 'tenant_admin', 'clinician'],
    },
    {
      text: 'Settings',
      icon: <Settings />,
      path: '/settings',
      userTypes: ['super_admin', 'tenant_admin', 'clinician'],
    },
  ];

  const hasPermission = (item: NavigationItem): boolean => {
    // Check user type
    if (item.userTypes && !item.userTypes.includes(userType!)) {
      return false;
    }

    // Check permissions
    if (item.permissions && user) {
      const userPermissions = userType === 'super_admin' 
        ? (user as any).permission 
        : (user as any).roleId?.permission || [];
      
      return item.permissions.some(permission => 
        userPermissions.includes(permission)
      );
    }

    return true;
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (onClose) {
      onClose();
    }
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const getUserTypeColor = () => {
    switch (userType) {
      case 'super_admin':
        return 'error';
      case 'tenant_admin':
        return 'primary';
      case 'clinician':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getUserTypeLabel = () => {
    switch (userType) {
      case 'super_admin':
        return 'Super Admin';
      case 'tenant_admin':
        return 'Tenant Admin';
      case 'clinician':
        return 'Clinician';
      default:
        return 'User';
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Toolbar sx={{ px: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SupervisorAccount sx={{ color: 'white' }} />
          <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
            Scribble
          </Typography>
        </Box>
      </Toolbar>

      <Box sx={{ px: 2, pb: 2 }}>
        <Chip
          label={getUserTypeLabel()}
          color={getUserTypeColor()}
          size="small"
          sx={{ 
            color: 'white',
            '& .MuiChip-label': { color: 'white' }
          }}
        />
      </Box>

      <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.12)' }} />

      <List sx={{ flexGrow: 1, py: 1 }}>
        {navigationItems
          .filter(hasPermission)
          .map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                selected={isActive(item.path)}
                sx={{
                  mx: 1,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.15)',
                    },
                  },
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  },
                }}
              >
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text} 
                  sx={{ 
                    '& .MuiListItemText-primary': { 
                      color: 'white',
                      fontSize: '0.875rem',
                    } 
                  }} 
                />
              </ListItemButton>
            </ListItem>
          ))}
      </List>

      <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.12)' }} />

      <List sx={{ py: 1 }}>
        {commonItems
          .filter(hasPermission)
          .map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                selected={isActive(item.path)}
                sx={{
                  mx: 1,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.15)',
                    },
                  },
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  },
                }}
              >
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text} 
                  sx={{ 
                    '& .MuiListItemText-primary': { 
                      color: 'white',
                      fontSize: '0.875rem',
                    } 
                  }} 
                />
              </ListItemButton>
            </ListItem>
          ))}
      </List>
    </Box>
  );
}

export default Sidebar;
