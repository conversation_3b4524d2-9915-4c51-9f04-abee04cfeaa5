import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { store } from '@/store';
import { useAppSelector } from '@/store';
import LoginPage from '@/pages/LoginPage';
import DashboardLayout from '@/components/layout/DashboardLayout';
import SuperAdminDashboard from '@/pages/SuperAdminDashboard';
import TenantManagement from '@/pages/TenantManagement';
import TenantAdminDashboard from '@/pages/TenantAdminDashboard';
import UserManagement from '@/pages/UserManagement';
import VisitManagement from '@/pages/VisitManagement';
import FormManagement from '@/pages/FormManagement';
import Settings from '@/pages/Settings';
import Profile from '@/pages/Profile';
import NotificationSnackbar from '@/components/common/NotificationSnackbar';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
  components: {
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1e293b',
          color: 'white',
        },
      },
    },
  },
});

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
}

function AppRoutes() {
  const { isAuthenticated, userType } = useAppSelector((state) => state.auth);

  return (
    <Routes>
      <Route 
        path="/login" 
        element={
          isAuthenticated ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <LoginPage />
          )
        } 
      />
      
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
        }
      >
        <Route index element={<Navigate to="/dashboard" replace />} />
        
        {/* Super Admin Routes */}
        {userType === 'super_admin' && (
          <>
            <Route path="dashboard" element={<SuperAdminDashboard />} />
            <Route path="tenants" element={<TenantManagement />} />
          </>
        )}
        
        {/* Tenant Admin Routes */}
        {(userType === 'tenant_admin' || userType === 'clinician') && (
          <>
            <Route path="dashboard" element={<TenantAdminDashboard />} />
            <Route path="users" element={<UserManagement />} />
            <Route path="visits" element={<VisitManagement />} />
            <Route path="forms" element={<FormManagement />} />
          </>
        )}
        
        {/* Common Routes */}
        <Route path="settings" element={<Settings />} />
        <Route path="profile" element={<Profile />} />
      </Route>
      
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex', minHeight: '100vh' }}>
            <AppRoutes />
            <NotificationSnackbar />
          </Box>
        </Router>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
