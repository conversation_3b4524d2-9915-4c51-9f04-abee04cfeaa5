import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Tenant, CreateTenantRequest, PaginatedResponse } from '@/types';
import { tenantService } from '@/services/tenantService';

interface TenantState {
  tenants: Tenant[];
  currentTenant: Tenant | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: TenantState = {
  tenants: [],
  currentTenant: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

// Async thunks
export const fetchTenants = createAsyncThunk(
  'tenant/fetchTenants',
  async (params: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await tenantService.getTenants(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tenants');
    }
  }
);

export const createTenant = createAsyncThunk(
  'tenant/createTenant',
  async (tenantData: CreateTenantRequest, { rejectWithValue }) => {
    try {
      const response = await tenantService.createTenant(tenantData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create tenant');
    }
  }
);

export const updateTenant = createAsyncThunk(
  'tenant/updateTenant',
  async ({ id, data }: { id: string; data: Partial<Tenant> }, { rejectWithValue }) => {
    try {
      const response = await tenantService.updateTenant(id, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update tenant');
    }
  }
);

export const deleteTenant = createAsyncThunk(
  'tenant/deleteTenant',
  async (id: string, { rejectWithValue }) => {
    try {
      await tenantService.deleteTenant(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete tenant');
    }
  }
);

export const getTenantById = createAsyncThunk(
  'tenant/getTenantById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await tenantService.getTenantById(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tenant');
    }
  }
);

const tenantSlice = createSlice({
  name: 'tenant',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentTenant: (state, action: PayloadAction<Tenant | null>) => {
      state.currentTenant = action.payload;
    },
    setPagination: (state, action: PayloadAction<Partial<TenantState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Tenants
      .addCase(fetchTenants.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTenants.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tenants = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
      })
      .addCase(fetchTenants.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create Tenant
      .addCase(createTenant.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createTenant.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tenants.unshift(action.payload);
      })
      .addCase(createTenant.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update Tenant
      .addCase(updateTenant.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTenant.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.tenants.findIndex(tenant => tenant._id === action.payload._id);
        if (index !== -1) {
          state.tenants[index] = action.payload;
        }
        if (state.currentTenant?._id === action.payload._id) {
          state.currentTenant = action.payload;
        }
      })
      .addCase(updateTenant.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Delete Tenant
      .addCase(deleteTenant.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTenant.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tenants = state.tenants.filter(tenant => tenant._id !== action.payload);
        if (state.currentTenant?._id === action.payload) {
          state.currentTenant = null;
        }
      })
      .addCase(deleteTenant.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get Tenant By ID
      .addCase(getTenantById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTenantById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTenant = action.payload;
      })
      .addCase(getTenantById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentTenant, setPagination } = tenantSlice.actions;
export default tenantSlice.reducer;
