import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Form, FormTemplate, FormType, Discipline, PaginatedResponse } from '@/types';
import { formService } from '@/services/formService';

interface FormState {
  forms: Form[];
  formTemplates: FormTemplate[];
  formTypes: FormType[];
  disciplines: Discipline[];
  currentForm: Form | null;
  currentFormTemplate: FormTemplate | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: FormState = {
  forms: [],
  formTemplates: [],
  formTypes: [],
  disciplines: [],
  currentForm: null,
  currentFormTemplate: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

// Async thunks
export const fetchForms = createAsyncThunk(
  'form/fetchForms',
  async (params: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await formService.getForms(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch forms');
    }
  }
);

export const fetchFormTemplates = createAsyncThunk(
  'form/fetchFormTemplates',
  async (params: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await formService.getFormTemplates(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch form templates');
    }
  }
);

export const fetchFormTypes = createAsyncThunk(
  'form/fetchFormTypes',
  async (_, { rejectWithValue }) => {
    try {
      const response = await formService.getFormTypes();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch form types');
    }
  }
);

export const fetchDisciplines = createAsyncThunk(
  'form/fetchDisciplines',
  async (_, { rejectWithValue }) => {
    try {
      const response = await formService.getDisciplines();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch disciplines');
    }
  }
);

export const createForm = createAsyncThunk(
  'form/createForm',
  async (formData: Partial<Form>, { rejectWithValue }) => {
    try {
      const response = await formService.createForm(formData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create form');
    }
  }
);

export const updateForm = createAsyncThunk(
  'form/updateForm',
  async ({ id, data }: { id: string; data: Partial<Form> }, { rejectWithValue }) => {
    try {
      const response = await formService.updateForm(id, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update form');
    }
  }
);

export const deleteForm = createAsyncThunk(
  'form/deleteForm',
  async (id: string, { rejectWithValue }) => {
    try {
      await formService.deleteForm(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete form');
    }
  }
);

export const createFormTemplate = createAsyncThunk(
  'form/createFormTemplate',
  async (templateData: Partial<FormTemplate>, { rejectWithValue }) => {
    try {
      const response = await formService.createFormTemplate(templateData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create form template');
    }
  }
);

export const updateFormTemplate = createAsyncThunk(
  'form/updateFormTemplate',
  async ({ id, data }: { id: string; data: Partial<FormTemplate> }, { rejectWithValue }) => {
    try {
      const response = await formService.updateFormTemplate(id, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update form template');
    }
  }
);

const formSlice = createSlice({
  name: 'form',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentForm: (state, action: PayloadAction<Form | null>) => {
      state.currentForm = action.payload;
    },
    setCurrentFormTemplate: (state, action: PayloadAction<FormTemplate | null>) => {
      state.currentFormTemplate = action.payload;
    },
    setPagination: (state, action: PayloadAction<Partial<FormState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Forms
      .addCase(fetchForms.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchForms.fulfilled, (state, action) => {
        state.isLoading = false;
        state.forms = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
      })
      .addCase(fetchForms.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch Form Templates
      .addCase(fetchFormTemplates.fulfilled, (state, action) => {
        state.formTemplates = action.payload.data;
      })
      // Fetch Form Types
      .addCase(fetchFormTypes.fulfilled, (state, action) => {
        state.formTypes = action.payload;
      })
      // Fetch Disciplines
      .addCase(fetchDisciplines.fulfilled, (state, action) => {
        state.disciplines = action.payload;
      })
      // Create Form
      .addCase(createForm.fulfilled, (state, action) => {
        state.forms.unshift(action.payload);
      })
      // Update Form
      .addCase(updateForm.fulfilled, (state, action) => {
        const index = state.forms.findIndex(form => form._id === action.payload._id);
        if (index !== -1) {
          state.forms[index] = action.payload;
        }
      })
      // Delete Form
      .addCase(deleteForm.fulfilled, (state, action) => {
        state.forms = state.forms.filter(form => form._id !== action.payload);
      })
      // Create Form Template
      .addCase(createFormTemplate.fulfilled, (state, action) => {
        state.formTemplates.unshift(action.payload);
      })
      // Update Form Template
      .addCase(updateFormTemplate.fulfilled, (state, action) => {
        const index = state.formTemplates.findIndex(template => template._id === action.payload._id);
        if (index !== -1) {
          state.formTemplates[index] = action.payload;
        }
      });
  },
});

export const { clearError, setCurrentForm, setCurrentFormTemplate, setPagination } = formSlice.actions;
export default formSlice.reducer;
