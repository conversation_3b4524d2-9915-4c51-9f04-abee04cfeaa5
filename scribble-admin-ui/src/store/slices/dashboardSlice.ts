import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DashboardKPIs } from '@/types';
import { dashboardService } from '@/services/dashboardService';

interface DashboardState {
  kpis: DashboardKPIs | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

const initialState: DashboardState = {
  kpis: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchDashboardKPIs = createAsyncThunk(
  'dashboard/fetchKPIs',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getKPIs();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch dashboard KPIs');
    }
  }
);

export const refreshDashboard = createAsyncThunk(
  'dashboard/refresh',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getKPIs();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to refresh dashboard');
    }
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setKPIs: (state, action: PayloadAction<DashboardKPIs>) => {
      state.kpis = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Dashboard KPIs
      .addCase(fetchDashboardKPIs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboardKPIs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.kpis = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboardKPIs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Refresh Dashboard
      .addCase(refreshDashboard.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refreshDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.kpis = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(refreshDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setKPIs } = dashboardSlice.actions;
export default dashboardSlice.reducer;
