import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Visit, Episode, PaginatedResponse } from '@/types';
import { visitService } from '@/services/visitService';

interface VisitState {
  visits: Visit[];
  episodes: Episode[];
  currentVisit: Visit | null;
  currentEpisode: Episode | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: VisitState = {
  visits: [],
  episodes: [],
  currentVisit: null,
  currentEpisode: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

// Async thunks
export const fetchVisits = createAsyncThunk(
  'visit/fetchVisits',
  async (params: { page?: number; limit?: number; filters?: string } = {}, { rejectWithValue }) => {
    try {
      const response = await visitService.getVisits(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch visits');
    }
  }
);

export const fetchEpisodes = createAsyncThunk(
  'visit/fetchEpisodes',
  async (params: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await visitService.getEpisodes(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch episodes');
    }
  }
);

export const createVisit = createAsyncThunk(
  'visit/createVisit',
  async (visitData: Partial<Visit>, { rejectWithValue }) => {
    try {
      const response = await visitService.createVisit(visitData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create visit');
    }
  }
);

export const updateVisit = createAsyncThunk(
  'visit/updateVisit',
  async ({ id, data }: { id: string; data: Partial<Visit> }, { rejectWithValue }) => {
    try {
      const response = await visitService.updateVisit(id, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update visit');
    }
  }
);

export const deleteVisit = createAsyncThunk(
  'visit/deleteVisit',
  async (id: string, { rejectWithValue }) => {
    try {
      await visitService.deleteVisit(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete visit');
    }
  }
);

export const getVisitById = createAsyncThunk(
  'visit/getVisitById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await visitService.getVisitById(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch visit');
    }
  }
);

const visitSlice = createSlice({
  name: 'visit',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentVisit: (state, action: PayloadAction<Visit | null>) => {
      state.currentVisit = action.payload;
    },
    setCurrentEpisode: (state, action: PayloadAction<Episode | null>) => {
      state.currentEpisode = action.payload;
    },
    setPagination: (state, action: PayloadAction<Partial<VisitState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Visits
      .addCase(fetchVisits.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVisits.fulfilled, (state, action) => {
        state.isLoading = false;
        state.visits = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
      })
      .addCase(fetchVisits.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch Episodes
      .addCase(fetchEpisodes.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchEpisodes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.episodes = action.payload.data;
      })
      .addCase(fetchEpisodes.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create Visit
      .addCase(createVisit.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createVisit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.visits.unshift(action.payload);
      })
      .addCase(createVisit.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update Visit
      .addCase(updateVisit.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateVisit.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.visits.findIndex(visit => visit._id === action.payload._id);
        if (index !== -1) {
          state.visits[index] = action.payload;
        }
        if (state.currentVisit?._id === action.payload._id) {
          state.currentVisit = action.payload;
        }
      })
      .addCase(updateVisit.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Delete Visit
      .addCase(deleteVisit.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteVisit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.visits = state.visits.filter(visit => visit._id !== action.payload);
        if (state.currentVisit?._id === action.payload) {
          state.currentVisit = null;
        }
      })
      .addCase(deleteVisit.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get Visit By ID
      .addCase(getVisitById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getVisitById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentVisit = action.payload;
      })
      .addCase(getVisitById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentVisit, setCurrentEpisode, setPagination } = visitSlice.actions;
export default visitSlice.reducer;
