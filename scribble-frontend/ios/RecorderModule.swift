//
//  RecorderModule.swift
//  Scribble
//
//  Drop-in: all segments are AAC .m4a + passthrough merges
//  - Preserves original public API & events (start/stop/pause/resume/recreateRecorder/deleteFile/mergeSegments/finalizeFromAnyPath
//    getSegmentsWithDuration/getSegmentCount/getSegmentDuration/getCurrentAndPreviousSegments)
//  - Keeps 20s segmentation
//  - Adds trickle-append (background) to keep finalize fast
//  - Uses AVAssetExportPresetPassthrough when compatible; safe fallback to AppleM4A
//  - Robust interruption handling + lifecycle + storage monitor retained
//
//  Created by Amit KS on 7/18/25. Updated drop-in by ChatGPT.
//

import React
import Foundation
import AVFoundation
import UIKit

@objc(RecorderModule)
class RecorderModule: RCTEventEmitter {
  // MARK: - Config
  private let SEGMENT_SECONDS: TimeInterval = 20
  private let SAMPLE_RATE: Double = 44100
  private let CHANNELS: Int = 1
  private let BITRATE_BPS: Int = 128_000
  private let BITRATE_TOLERANCE: Double = 0.2 // ±20%
  private let storageThresholdMB: Int64 = 20
  private let USE_CHECKPOINTS = false
  private let CHECKPOINT_EVERY_N_SEGMENTS = 60 // ~20 minutes

  // MARK: - State
  private var audioRecorder: AVAudioRecorder?
  private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
  private var recordingURL: URL? // legacy surface

  private var sessionId: String = UUID().uuidString
  private var segmentIndex: Int = 0
  private var currentSegmentURL: URL?
  private var rolloverTimer: Timer?
  private var storageTimer: Timer?
  private var isObservingInterruption = false

  // Trickle-append queue
  private let appendQueue = DispatchQueue(label: "recorder.append.queue")
  private var isAppendBusy = false
  private var pendingAppends: [URL] = []
  private var checkpointIndex: Int = 0
  private var segmentsSinceCheckpoint: Int = 0

  private let STORED_SEGMENTS_KEY = "recorder.storedSegments"
  private let STORED_SESSION_DIR_KEY  = "recorder.sessionDir"
  private let persistQueue = DispatchQueue(label: "recorder.persist.queue", qos: .utility)
  private let NO_MERGE_MODE = true  // <-- turn on "store raw segments only"


  // MARK: - Duration bookkeeping (as in original)
  private struct SessionDurationData { var totalDuration: TimeInterval; var lastProcessedChunk: String; var sessionId: String }
  private func durationStorageKey(for sessionId: String) -> String { "RecordingSession_\(sessionId)_Duration" }
  private func saveDurationData(_ data: SessionDurationData) {
    UserDefaults.standard.set([
      "totalDuration": data.totalDuration,
      "lastProcessedChunk": data.lastProcessedChunk,
      "sessionId": data.sessionId,
      "lastUpdated": Date().timeIntervalSince1970
    ], forKey: durationStorageKey(for: data.sessionId))
  }
  private func loadDurationData(for sessionId: String) -> SessionDurationData? {
    guard let d = UserDefaults.standard.dictionary(forKey: durationStorageKey(for: sessionId)),
          let total = d["totalDuration"] as? TimeInterval,
          let last = d["lastProcessedChunk"] as? String,
          let sid = d["sessionId"] as? String else { return nil }
    return SessionDurationData(totalDuration: total, lastProcessedChunk: last, sessionId: sid)
  }

  // MARK: - RN Wiring
  override static func requiresMainQueueSetup() -> Bool { false }
  override func supportedEvents() -> [String]! {
    ["RecordingInterruption","RecordingSegmentCreated","RecordingRecovered","RecordingStorage","MemoryLowInterruption","FinalizingProgress","RecordingSegmentReady"]
  }

  override init() {
    super.init()
    installLifecycleObservers()
    
  }
  deinit { NotificationCenter.default.removeObserver(self) }

  // MARK: - Public API (preserved)
  @objc(startRecording:withRejecter:)
  func startRecording(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    let session = AVAudioSession.sharedInstance()
    session.requestRecordPermission { [weak self] granted in
      guard let self = self else { return }
      guard granted else { reject("PERMISSION_ERR","Microphone permission not granted",nil); return }
      DispatchQueue.main.async {
        do {
          try session.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker,.allowBluetooth,.allowBluetoothA2DP])
          try session.setActive(true)
          self.ensureInterruptionObserverInstalled()
          self.beginBGTask()
          self.sessionId = UUID().uuidString
          self.segmentIndex = 0
          self.currentSegmentURL = nil
          self.checkpointIndex = 0
          self.segmentsSinceCheckpoint = 0
          self.checkStorageAndEmit(context: "startRecording")
          try self.startSegment()
          self.startRolloverTimer()
          if let seg = self.currentSegmentURL { self.recordingURL = seg; resolve(seg.absoluteString) }
          else { reject("RECORD_ERR","Segment did not start",nil) }
        } catch { reject("RECORD_ERR","Failed to start recording",error) }
      }
    }
  }

  @objc(stopRecording:withRejecter:)
  func stopRecording(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    rolloverTimer?.invalidate(); rolloverTimer = nil
    if let r = audioRecorder, r.isRecording { r.stop() }
    audioRecorder = nil
    NotificationCenter.default.removeObserver(self, name: AVAudioSession.interruptionNotification, object: AVAudioSession.sharedInstance());
    isObservingInterruption = false
    endBGTask()

    if NO_MERGE_MODE {
       let dir = self.sessionDir()
       let parts = self.collectTailSegments(dir: dir)  // seg_*.m4a only
       resolve([
         "dir": dir.absoluteString,
         "segments": parts.map { $0.absoluteString }
       ])
       return
     }
    
    // Flush trickle appends
    flushPendingAppendsSync()

    DispatchQueue.global(qos: .userInitiated).async {
      let dir = self.sessionDir()
      var parts: [URL] = []
      if self.USE_CHECKPOINTS {
        parts.append(contentsOf: self.collectCheckpointFinals(dir: dir))
      } else {
        let rolling = dir.appendingPathComponent("final.m4a"); if FileManager.default.fileExists(atPath: rolling.path) { parts.append(rolling) }
      }
      parts.append(contentsOf: self.collectTailSegments(dir: dir))
      guard !parts.isEmpty else { reject("MERGE_ERR","No audio sources to merge",nil); return }
      let outURL = dir.appendingPathComponent("final.m4a")
      self.mergeWithAutoPreset(parts, outURL: outURL, context: "finalize") { res in
        switch res {
        case .success(let url): self.cleanupTail(dir: dir); resolve(url.absoluteString)
        case .failure(let err): reject("MERGE_ERR","Failed to merge segments",err)
        }
      }
    }
  }

//  @objc(pauseRecording:withRejecter:)
//  func pauseRecording(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
//    if let r = audioRecorder, r.isRecording { r.pause(); resolve(true) } else { reject("PAUSE_ERR","No active recorder to pause",nil) }
//  }
//
  
  @objc(pauseRecording:withRejecter:)
  func pauseRecording(_ resolve: @escaping RCTPromiseResolveBlock,
                      reject: @escaping RCTPromiseRejectBlock) {
    if let r = audioRecorder, r.isRecording {
      r.pause()
     // self.rolloverSegment()
      // IMPORTANT: stop the 20s cadence
      rolloverTimer?.invalidate()
      rolloverTimer = nil
      finalizeCurrentSegment(reason: "pause")
      resolve(true)
    } else {
      reject("PAUSE_ERR","No active recorder to pause",nil)
    }
  }


//  @objc(resumeRecording:withRejecter:)
//  func resumeRecording(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
//    guard let r = audioRecorder else { reject("RESUME_ERR","Recorder not initialized. Start recording first.",nil); return }
//    try? AVAudioSession.sharedInstance().setActive(true);
//    ensureInterruptionObserverInstalled()
//    if !r.isRecording { r.record() }
//    resolve(true)
//  }
  
  private func finalizeCurrentSegment(reason: String) {
    guard let rec = audioRecorder else { return }

    // Stop even if paused
    rec.stop()
    audioRecorder = nil

    let finishedIndex = segmentIndex
    guard let u = currentSegmentURL else { return }

    // duration, event, remember, and queue append (same as rollover)
    accumulateDurationForCurrentChunk(u)
    durationOfFile(u) { dur in
      self.sendEvent(withName: "RecordingSegmentReady",
        body: ["path": u.absoluteString, "index": finishedIndex, "duration": dur as Any, "reason": reason])
      self.rememberSegment(url: u)
    }
    if !NO_MERGE_MODE {
      if self.USE_CHECKPOINTS {
        self.segmentsSinceCheckpoint += 1
        if self.segmentsSinceCheckpoint >= self.CHECKPOINT_EVERY_N_SEGMENTS {
          self.segmentsSinceCheckpoint = 0
          self.checkpointIndex += 1
          self.enqueueAppend(of: u, target: self.checkpointURL(in: u.deletingLastPathComponent(), idx: self.checkpointIndex))
        } else {
          self.enqueueAppend(of: u, target: u.deletingLastPathComponent().appendingPathComponent("final.m4a"))
        }
      } else {
        self.enqueueAppend(of: u, target: u.deletingLastPathComponent().appendingPathComponent("final.m4a"))
      }
    }

    // IMPORTANT: do NOT start a new segment here; pause should end the file.
    self.currentSegmentURL = nil
  }
  
//  @objc(resumeRecording:withRejecter:)
//  func resumeRecording(_ resolve: @escaping RCTPromiseResolveBlock,
//                       reject: @escaping RCTPromiseRejectBlock) {
//    guard let r = audioRecorder else {
//      reject("RESUME_ERR","Recorder not initialized. Start recording first.",nil)
//      return
//    }
//
//    try? AVAudioSession.sharedInstance().setActive(true)
//    ensureInterruptionObserverInstalled()
//
//    if !r.isRecording { r.record() }
//
//    // Ensure the recorder is actually running on the runloop before we roll
//    DispatchQueue.main.async {
//      // (a) Force a clean boundary so next segment isn't oversized
// //  self.rolloverSegment()
//    
//
//      // (b) Restart 20s schedule aligned from "now"
//      self.startRolloverTimer()
//
//      resolve(true)
//    }
//  }
  @objc(resumeRecording:withRejecter:)
  func resumeRecording(_ resolve: @escaping RCTPromiseResolveBlock,
                       reject: @escaping RCTPromiseRejectBlock) {
    do {
      try AVAudioSession.sharedInstance().setActive(true)
    } catch {
      reject("RESUME_ERR","Failed to activate audio session", error)
      return
    }
    ensureInterruptionObserverInstalled()

    // We finalized on pause, so start a brand-new segment now
    DispatchQueue.main.async {
      do {
        try self.startSegment()            // creates seg_XXXX.m4a and starts recording
        self.startRolloverTimer()          // restart 20s cadence aligned from now
        resolve(true)
      } catch {
        reject("RESUME_ERR","Failed to start new segment after resume", error)
      }
    }
  }




  @objc
  func recreateRecorder(_ filePath: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    do {
      try AVAudioSession.sharedInstance().setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker,.allowBluetooth,.allowBluetoothA2DP])
      try AVAudioSession.sharedInstance().setActive(true)
      ensureInterruptionObserverInstalled()
      let inURL = (URL(string: filePath)?.scheme == "file") ? URL(string: filePath)! : URL(fileURLWithPath: filePath)
      let dir = inURL.deletingLastPathComponent(); self.sessionId = dir.lastPathComponent
      // Start a fresh segment in same dir then pause (caller may resume)
      self.currentSegmentURL = nil
      try self.startSegment(in: dir)
      self.audioRecorder?.pause()
      guard let cur = self.currentSegmentURL else { reject("RECREATE_ERR","Failed to create a new segment",nil); return }
      resolve(cur.absoluteString)
    } catch { reject("RECREATE_ERR","Failed to recreate recorder",error) }
  }

  @objc
  func deleteFile(_ filePath: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    let url: URL = (URL(string: filePath)?.scheme == "file") ? URL(string: filePath)! : URL(fileURLWithPath: filePath)
    if let cur = self.currentSegmentURL, cur == url { audioRecorder?.stop(); audioRecorder = nil; self.currentSegmentURL = nil }
    if let legacy = self.recordingURL, legacy == url { audioRecorder?.stop(); audioRecorder = nil; self.recordingURL = nil }
    do {
      if FileManager.default.fileExists(atPath: url.path) { try FileManager.default.removeItem(at: url); resolve(["deleted":true,"existed":true]) }
      else { resolve(["deleted":false,"existed":false]) }
    } catch { reject("DELETE_ERR","Failed to delete file at path: \(url.path)",error) }
  }

  @objc
  func mergeSegments(_ segmentPaths: [String], resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    let urls = segmentPaths.map { (URL(string: $0)?.scheme == "file") ? URL(string: $0)! : URL(fileURLWithPath: $0) }
    let outURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(UUID().uuidString).m4a")
    mergeWithAutoPreset(urls, outURL: outURL, context: "manualMerge") { r in
      switch r { case .success(let u): resolve(u.absoluteString); case .failure(let e): reject("MERGE_ERR","Export failed",e) }
    }
  }
//  
//  @objc
//  func getStoredSegments(_ resolve: @escaping RCTPromiseResolveBlock,
//                        reject: @escaping RCTPromiseRejectBlock) {
//      
//      DispatchQueue.global(qos: .utility).async {
//          let storedSegments = self.loadStoredSegments()
//          let fileManager = FileManager.default
//          
//          // Filter out files that no longer exist and gather metadata
//          var validSegments: [[String: Any]] = []
//          
//          for segmentURLString in storedSegments {
//              guard let url = URL(string: segmentURLString) else { continue }
//              
//              // Check if file still exists
//              guard fileManager.fileExists(atPath: url.path) else { continue }
//            
//              // Get file attributes
////            durationOfFile(url.lastPathComponent) { dur in }
//              var segmentInfo: [String: Any] = [
//                  "path": segmentURLString,
//                  "name": url.lastPathComponent,
//                
//              ]
//          
//              self.durationOfFile(url) { dur in
//               segmentInfo["duration"] = dur
//              }
//              
//              // Add file size
//              if let attributes = try? fileManager.attributesOfItem(atPath: url.path) {
//                  if let size = attributes[.size] as? NSNumber {
//                      segmentInfo["size"] = size.int64Value
//                  }
////                  if let creationDate = attributes[.creationDate] as? Date {
////                      segmentInfo["creationDate"] = creationDate.timeIntervalSince1970
////                  }
////                  if let modificationDate = attributes[.modificationDate] as? Date {
////                      segmentInfo["modificationDate"] = modificationDate.timeIntervalSince1970
////                  }
//              }
//            
//            
//              
//              validSegments.append(segmentInfo)
//          }
//          
//          // Update stored segments to remove invalid ones
//          let validPaths = validSegments.map { $0["path"] as! String }
//          if validPaths.count != storedSegments.count {
//              self.saveStoredSegments(validPaths)
//          }
//          
//          DispatchQueue.main.async {
//              resolve([
//                  "segments": validSegments,
//                  "totalCount": validSegments.count
//              ])
//          }
//      }
//  }
  
  @objc
  func getStoredSegments(_ resolve: @escaping RCTPromiseResolveBlock,
                         reject: @escaping RCTPromiseRejectBlock) {

    DispatchQueue.global(qos: .utility).async {
      let storedSegments = self.loadStoredSegments()
      let fileManager = FileManager.default

      // Early out if nothing to do
      if storedSegments.isEmpty {
        DispatchQueue.main.async {
          resolve(["segments": [], "totalCount": 0])
        }
        return
      }

      var validSegments: [[String: Any]] = []
      validSegments.reserveCapacity(storedSegments.count)

      // Serialize writes to `validSegments`
      let writeQueue = DispatchQueue(label: "recorder.getStoredSegments.write")

      // Track all async duration loads
      let group = DispatchGroup()

      for segmentURLString in storedSegments {
        // Robust file URL handling: supports "file://..." and raw paths
        let maybeURL: URL? = {
          if let u = URL(string: segmentURLString), u.scheme == "file" { return u }
          return URL(fileURLWithPath: segmentURLString)
        }()

        guard let url = maybeURL, fileManager.fileExists(atPath: url.path) else { continue }

        // Base info
        var info: [String: Any] = [
          "path": segmentURLString,
          "name": url.lastPathComponent
        ]

        // File size (optional)
        if let attrs = try? fileManager.attributesOfItem(atPath: url.path),
           let size = attrs[.size] as? NSNumber {
          info["size"] = size.int64Value
        }

        // Append placeholder and remember index to update later
        var myIndex = 0
        writeQueue.sync {
          myIndex = validSegments.count
          validSegments.append(info)
        }

        // Fetch duration asynchronously and update the exact element by index
        group.enter()
        self.durationOfFile(url) { dur in
          writeQueue.sync {
            var updated = validSegments[myIndex]
            if let d = dur { updated["duration"] = d }
            validSegments[myIndex] = updated
          }
          group.leave()
        }
      }

      // When all durations are done, prune invalids and resolve
      group.notify(queue: .global(qos: .utility)) {
        // Update stored segments to remove invalid ones
        let validPaths = validSegments.compactMap { $0["path"] as? String }
        if validPaths.count != storedSegments.count {
          self.saveStoredSegments(validPaths)
        }

        DispatchQueue.main.async {
          resolve([
            "segments": validSegments,
            "totalCount": validSegments.count
          ])
        }
      }
    }
  }


  // MARK: - Segmentation
  private var aacSettings: [String: Any] {
    [AVFormatIDKey: Int(kAudioFormatMPEG4AAC), AVSampleRateKey: SAMPLE_RATE, AVNumberOfChannelsKey: CHANNELS, AVEncoderBitRateKey: BITRATE_BPS, AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue]
  }

  private func startSegment(in dir: URL? = nil) throws {
    checkStorageAndEmit(context: "startSegment")
    let d = try dir ?? ensureSessionDir()
    let url = nextSegmentURL(in: d)
    currentSegmentURL = url
    let rec = try AVAudioRecorder(url: url, settings: aacSettings)
    rec.isMeteringEnabled = true
    rec.prepareToRecord()
    guard rec.record() else { throw NSError(domain: "RECORD_ERR", code: -1, userInfo: [NSLocalizedDescriptionKey:"Could not record"]) }
    audioRecorder = rec
    try? FileManager.default.setAttributes([.protectionKey: FileProtectionType.completeUntilFirstUserAuthentication], ofItemAtPath: url.path)
    sendEvent(withName: "RecordingSegmentCreated", body: ["path": url.absoluteString, "index": segmentIndex])
  }

  private func rolloverSegment() {
    guard let rec = audioRecorder, rec.isRecording else { return }
    rec.stop(); audioRecorder = nil
    
    let finishedIndex = segmentIndex
     let finishedURL = currentSegmentURL
    print("finishedURL",finishedURL)
  //  let duration = getSegmentDuration(finishedURL)!
    
    if let u = currentSegmentURL {
      // Duration bookkeeping (current segment as new chunk)
      accumulateDurationForCurrentChunk(u)
      // enqueue append
      //      sendEvent(withName: "RecordingSegmentReady",
      //                    body: ["path": u.absoluteString, "index": finishedIndex])
      
      durationOfFile(u) { dur in
        self.sendEvent(withName: "RecordingSegmentReady",
                       body: [
                        "path": u.absoluteString,
                        "index": finishedIndex,
                        "duration": dur as Any // will be Double or nil
                       ])
        self.rememberSegment(url: u)
      }
      
      
      
      if !NO_MERGE_MODE {
        if USE_CHECKPOINTS {
          segmentsSinceCheckpoint += 1
          if segmentsSinceCheckpoint >= CHECKPOINT_EVERY_N_SEGMENTS {
            segmentsSinceCheckpoint = 0; checkpointIndex += 1
            enqueueAppend(of: u, target: checkpointURL(in: u.deletingLastPathComponent(), idx: checkpointIndex))
          } else { enqueueAppend(of: u, target: u.deletingLastPathComponent().appendingPathComponent("final.m4a")) }
        } else { enqueueAppend(of: u, target: u.deletingLastPathComponent().appendingPathComponent("final.m4a")) }
      }
    }

    do { try startSegment() } catch { /* best-effort restart */ }
  }

  private func startRolloverTimer() {
    rolloverTimer?.invalidate()
    rolloverTimer = Timer.scheduledTimer(withTimeInterval: SEGMENT_SECONDS, repeats: true) { [weak self] _ in self?.rolloverSegment() }
    RunLoop.main.add(rolloverTimer!, forMode: .common)
    startStorageMonitor()
  }

  private func nextSegmentURL(in dir: URL) -> URL {
    var idx = segmentIndex
    let fm = FileManager.default
    repeat { idx += 1 } while fm.fileExists(atPath: dir.appendingPathComponent(String(format: "seg_%04d.m4a", idx)).path)
    segmentIndex = idx
    return dir.appendingPathComponent(String(format: "seg_%04d.m4a", idx))
  }

  // MARK: - Trickle Append
  private func enqueueAppend(of newSegment: URL, target outURL: URL) {
    appendQueue.async { self.pendingAppends.append(newSegment); self.drainAppendQueueIfNeeded(target: outURL) }
  }

  private func flushPendingAppendsSync() {
    appendQueue.sync {
      if NO_MERGE_MODE { return }
      // wait until current export (if any) finishes
      while isAppendBusy { usleep(50_000) }
      // NEW: keep draining until queue is empty
      while !pendingAppends.isEmpty {
        // kick the drain (if not paused)
        if !mergeQueue.sync(execute: { self.isAppendPaused }) {
          self.drainAppendQueueIfNeeded(target: self.sessionDir().appendingPathComponent("final.m4a"))
        }
        // wait a bit for it to process
        usleep(80_000)
        // also wait while busy
        while isAppendBusy { usleep(50_000) }
      }
    }
  }

  private func checkpointURL(in dir: URL, idx: Int) -> URL { dir.appendingPathComponent(String(format: "final_%04d.m4a", idx)) }

  // MARK: - Merge (auto-preset)
  private func mergeWithAutoPreset(_ inputs: [URL], outURL: URL, context: String, completion: @escaping (Result<URL, Error>) -> Void) {
    let fm = FileManager.default
    let candidates = inputs.filter { fm.fileExists(atPath: $0.path) && (((try? fm.attributesOfItem(atPath: $0.path)[.size] as? NSNumber)?.intValue ?? 0) > 0) }
    guard !candidates.isEmpty else { completion(.failure(NSError(domain: "MERGE_ERR", code: -10, userInfo: [NSLocalizedDescriptionKey:"No valid inputs"]))); return }

    let comp = AVMutableComposition(); guard let track = comp.addMutableTrack(withMediaType: .audio, preferredTrackID: kCMPersistentTrackID_Invalid) else { completion(.failure(NSError(domain: "MERGE_ERR", code: -11, userInfo: [NSLocalizedDescriptionKey:"Track create failed"]))); return }

    var cursor = CMTime.zero
    let group = DispatchGroup()
    var loadError: Error?
    var compat = CompatibilityAccumulator(targetSR: SAMPLE_RATE, targetCh: CHANNELS, targetBitrate: BITRATE_BPS, tolerance: BITRATE_TOLERANCE)

    for u in candidates {
      let asset = AVURLAsset(url: u, options: [AVURLAssetPreferPreciseDurationAndTimingKey: true])
      group.enter()
      asset.loadValuesAsynchronously(forKeys: ["tracks","duration","playable"]) {
        defer { group.leave() }
        var e: NSError?
        for key in ["tracks","duration","playable"] { let s = asset.statusOfValue(forKey: key, error: &e); if s == .failed || s == .cancelled { loadError = e; return } }
        guard asset.isPlayable, let aTrack = asset.tracks(withMediaType: .audio).first else { return }
        let dur = asset.duration; guard dur.isValid && !dur.isIndefinite && dur.seconds > 0 else { return }
        do { try track.insertTimeRange(CMTimeRange(start: .zero, duration: dur), of: aTrack, at: cursor); cursor = cursor + dur } catch { loadError = error; return }
        compat.observe(asset: asset, track: aTrack)
      }
    }

    group.notify(queue: .global(qos: .userInitiated)) {
      if let e = loadError { completion(.failure(e)); return }
      let canPT = compat.isLikelyPassthroughCompatible()
      let preset = canPT ? AVAssetExportPresetPassthrough : AVAssetExportPresetAppleM4A
      try? fm.removeItem(at: outURL)
      guard let exporter = AVAssetExportSession(asset: comp, presetName: preset) else { completion(.failure(NSError(domain: "MERGE_ERR", code: -12, userInfo: [NSLocalizedDescriptionKey:"No exporter"]))); return }
      exporter.outputURL = outURL; exporter.outputFileType = .m4a
      let timer = Timer(timeInterval: 0.2, repeats: true) { [weak self] t in
        guard let self = self else { t.invalidate(); return }
        DispatchQueue.main.async { self.sendEvent(withName: "FinalizingProgress", body: ["progress": exporter.progress, "context": context, "preset": preset]) }
        if exporter.status != .exporting { t.invalidate() }
      }
      RunLoop.main.add(timer, forMode: .common)
      exporter.exportAsynchronously {
        switch exporter.status {
        case .completed:
          DispatchQueue.main.async { self.sendEvent(withName: "FinalizingProgress", body: ["progress": 1.0, "context": context, "preset": preset]) }
          completion(.success(outURL))
        case .failed, .cancelled:
          completion(.failure(exporter.error ?? NSError(domain: "MERGE_ERR", code: -13, userInfo: [NSLocalizedDescriptionKey:"Export failed"])) )
        default: break
        }
      }
    }
  }

  private struct CompatibilityAccumulator {
    let targetSR: Double; let targetCh: Int; let targetBitrate: Int; let tolerance: Double
    var allAAC = true; var allSR = true; var allCH = true; var allBR = true
    mutating func observe(asset: AVAsset, track: AVAssetTrack) {
      var isAAC = false
      for fd in track.formatDescriptions { let fmt = fd as! CMAudioFormatDescription; if let asbd = CMAudioFormatDescriptionGetStreamBasicDescription(fmt)?.pointee { if asbd.mFormatID == kAudioFormatMPEG4AAC { isAAC = true }; if abs(asbd.mSampleRate - targetSR) > 0.1 { allSR = false }; if Int(asbd.mChannelsPerFrame) != targetCh { allCH = false } } }
      if !isAAC { allAAC = false }
      let est = track.estimatedDataRate // bits/s
      if est > 0 {
        let low = Double(targetBitrate) * (1.0 - tolerance); let high = Double(targetBitrate) * (1.0 + tolerance)
        if !(Double(est) >= low && Double(est) <= high) { allBR = false }
      }
    }
    func isLikelyPassthroughCompatible() -> Bool { allAAC && allSR && allCH && allBR }
  }

  // MARK: - Interruption
  private func ensureInterruptionObserverInstalled() {
    guard !isObservingInterruption else { return }
    NotificationCenter.default.addObserver(self, selector: #selector(handleInterruption), name: AVAudioSession.interruptionNotification, object: AVAudioSession.sharedInstance())
    isObservingInterruption = true
  }
  @objc private func handleInterruption(notification: Notification) {
    guard let userInfo = notification.userInfo,
          let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
          let type = AVAudioSession.InterruptionType(rawValue: typeValue) else { return }
    switch type {
    case .began:
      audioRecorder?.pause()
      sendEvent(withName: "RecordingInterruption", body: ["status":"paused"]) // original behavior
    case .ended:
      do { try AVAudioSession.sharedInstance().setActive(true) } catch { sendEvent(withName: "RecordingInterruption", body: ["status":"error"]); return }
      let shouldResume: Bool = {
        if let v = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt { return AVAudioSession.InterruptionOptions(rawValue: v).contains(.shouldResume) }
        return true // permissive default, as in original variant
      }()
      if shouldResume, let r = audioRecorder, !r.isRecording { r.record(); sendEvent(withName: "RecordingInterruption", body: ["status":"resumed"]) }
    @unknown default: break
    }
  }

  // MARK: - Lifecycle & BG task
  private func installLifecycleObservers() {
    NotificationCenter.default.addObserver(self, selector: #selector(appWillResignActive), name: UIApplication.willResignActiveNotification, object: nil)
    NotificationCenter.default.addObserver(self, selector: #selector(appDidEnterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
  }
  @objc private func appWillResignActive() { rolloverSegment() }
  @objc private func appDidEnterBackground() { rolloverSegment() }

  private func beginBGTask() {
    endBGTask()
    backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in guard let self = self else { return }; UIApplication.shared.endBackgroundTask(self.backgroundTask); self.backgroundTask = .invalid }
  }
  private func endBGTask() { if backgroundTask != .invalid { UIApplication.shared.endBackgroundTask(backgroundTask); backgroundTask = .invalid } }

  // MARK: - Storage monitor
  private func startStorageMonitor() {
    storageTimer?.invalidate()
    storageTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
      guard let self = self else { return }
      let freeMB = self.availableDiskMB();
      if freeMB >= 0 && freeMB < self.storageThresholdMB { self.audioRecorder?.pause(); self.sendEvent(withName: "MemoryLowInterruption", body: ["status":"paused"]) }
    }
    RunLoop.main.add(storageTimer!, forMode: .common)
  }
  private func stopStorageMonitor() { storageTimer?.invalidate(); storageTimer = nil }
  private func availableDiskMB() -> Int64 {
    let base = recordingsBaseDir()
    if let values = try? base.resourceValues(forKeys: [.volumeAvailableCapacityForImportantUsageKey,.volumeAvailableCapacityKey]) {
      if let imp = values.volumeAvailableCapacityForImportantUsage { return Int64(imp)/(1024*1024) }
      if let gen = values.volumeAvailableCapacity { return Int64(gen)/(1024*1024) }
    }
    return -1
  }
  private func checkStorageAndEmit(context: String) { let freeMB = availableDiskMB();  }

  // MARK: - Collect / Cleanup
  private func collectTailSegments(dir: URL) -> [URL] {
    let fm = FileManager.default
    return ((try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles])) ?? [])
      .filter { $0.pathExtension.lowercased() == "m4a" && $0.lastPathComponent.hasPrefix("seg_") }
      .sorted { $0.lastPathComponent < $1.lastPathComponent }
  }
  private func collectCheckpointFinals(dir: URL) -> [URL] {
    let fm = FileManager.default
    return ((try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles])) ?? [])
      .filter { $0.pathExtension.lowercased() == "m4a" && $0.lastPathComponent.hasPrefix("final_") }
      .sorted { $0.lastPathComponent < $1.lastPathComponent }
  }
  private func cleanupTail(dir: URL) {
    let fm = FileManager.default
    let all = ((try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles])) ?? [])
    for u in all { let n = u.lastPathComponent; if n.hasPrefix("seg_") && n.hasSuffix(".m4a") { try? fm.removeItem(at: u) }; if USE_CHECKPOINTS && n.hasPrefix("final_") && n.hasSuffix(".m4a") { try? fm.removeItem(at: u) } }
  }

  // MARK: - Helpers for duration accumulation (used by getSegmentsWithDuration)
  private func accumulateDurationForCurrentChunk(_ url: URL) {
    let dir = url.deletingLastPathComponent(); let sid = dir.lastPathComponent
    var data = loadDurationData(for: sid) ?? SessionDurationData(totalDuration: 0, lastProcessedChunk: "", sessionId: sid)
    let name = url.lastPathComponent
    if data.lastProcessedChunk != name {
      let asset = AVURLAsset(url: url)
      asset.loadValuesAsynchronously(forKeys: ["duration"]) {
        var err: NSError?
        if asset.statusOfValue(forKey: "duration", error: &err) == .loaded {
          let d = asset.duration; if d.isValid && !d.isIndefinite { data.totalDuration += d.seconds; data.lastProcessedChunk = name; self.saveDurationData(data) }
        }
      }
    }
  }

  // MARK: - Introspection helpers (preserved API)
  @objc
  func getSegmentsWithDuration(_ urlPath: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    DispatchQueue.global(qos: .utility).async {
      let inputURL: URL = (URL(string: urlPath)?.scheme == "file") ? URL(string: urlPath)! : URL(fileURLWithPath: urlPath)
      let directory = inputURL.hasDirectoryPath ? inputURL : inputURL.deletingLastPathComponent()
      let fm = FileManager.default
      guard let contents = try? fm.contentsOfDirectory(at: directory, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles]) else { DispatchQueue.main.async { reject("SEGMENTS_ERR","Could not read directory contents",nil) }; return }
      let segs = contents.filter { $0.pathExtension.lowercased() == "m4a" && $0.lastPathComponent.hasPrefix("seg_") }.sorted { $0.lastPathComponent < $1.lastPathComponent }
      guard let current = segs.last else { DispatchQueue.main.async { reject("SEGMENTS_ERR","No segment files found in directory",nil) }; return }
      let sid = directory.lastPathComponent
      let data = self.loadDurationData(for: sid) ?? SessionDurationData(totalDuration: 0, lastProcessedChunk: "", sessionId: sid)
      let result: [String: Any] = [
        "currentSegmentName": current.lastPathComponent,
        "currentSegmentIndex": self.numericSegIndex(current),
        "totalSegments": segs.count,
        "cumulativeDuration": data.totalDuration,
        "sessionId": sid
      ]
      DispatchQueue.main.async { resolve(result) }
    }
  }

  @objc func getSegmentCount(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) { resolve(["totalSegments": numericIndexMax(in: sessionDir()), "currentSegmentIndex": segmentIndex, "sessionId": sessionId]) }

  @objc func getSegmentDuration(_ segmentPath: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    let u: URL = (URL(string: segmentPath)?.scheme == "file") ? URL(string: segmentPath)! : URL(fileURLWithPath: segmentPath)
    guard FileManager.default.fileExists(atPath: u.path) else { reject("SEGMENT_ERR","Segment file does not exist",nil); return }
    let asset = AVURLAsset(url: u); asset.loadValuesAsynchronously(forKeys: ["duration"]) {
      var e: NSError?; let st = asset.statusOfValue(forKey: "duration", error: &e)
      DispatchQueue.main.async { guard st == .loaded, e == nil else { reject("DURATION_ERR","Failed to load duration",e); return }
        let d = asset.duration; guard d.isValid && !d.isIndefinite else { reject("DURATION_ERR","Invalid duration",nil); return }
        resolve(["duration": d.seconds, "segmentPath": segmentPath, "segmentName": u.lastPathComponent])
      }
    }
  }

  @objc func getCurrentAndPreviousSegments(_ urlPath: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    let inputURL: URL = (URL(string: urlPath)?.scheme == "file") ? URL(string: urlPath)! : URL(fileURLWithPath: urlPath)
    let directory = inputURL.hasDirectoryPath ? inputURL : inputURL.deletingLastPathComponent()
    let fm = FileManager.default
    guard let contents = try? fm.contentsOfDirectory(at: directory, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles]) else { reject("SEGMENTS_ERR","Could not read directory contents",nil); return }
    let segs = contents.filter { $0.pathExtension.lowercased() == "m4a" && $0.lastPathComponent.hasPrefix("seg_") }.sorted { $0.lastPathComponent < $1.lastPathComponent }
    guard !segs.isEmpty else { reject("SEGMENTS_ERR","No segment files found in directory",nil); return }
    let current = segs.last!; let prev = segs.count > 1 ? segs[segs.count-2] : nil
    var result: [String: Any] = ["currentSegment": current.absoluteString, "currentSegmentIndex": numericSegIndex(current), "totalSegments": segs.count]
    if let p = prev { result["previousSegment"] = p.absoluteString; result["previousSegmentIndex"] = numericSegIndex(p) }
    resolve(result)
  }

  // MARK: - Index helpers & dirs
  private func numericSegIndex(_ url: URL) -> Int { Int(url.deletingPathExtension().lastPathComponent.split(separator: "_").last ?? "0") ?? 0 }
  private func numericIndexMax(in dir: URL) -> Int {
    let fm = FileManager.default
    let segs = ((try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles])) ?? []).filter { $0.pathExtension.lowercased() == "m4a" && $0.lastPathComponent.hasPrefix("seg_") }
    return segs.map { numericSegIndex($0) }.max() ?? 0
  }
  private func recordingsBaseDir() -> URL {
    let fm = FileManager.default
    let base = try! fm.url(for: .applicationSupportDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
    let dir = base.appendingPathComponent("Recordings", isDirectory: true)
    if !fm.fileExists(atPath: dir.path) { try? fm.createDirectory(at: dir, withIntermediateDirectories: true) }
    var values = URLResourceValues(); values.isExcludedFromBackup = true; var m = dir; try? m.setResourceValues(values)
    return dir
  }
  private func sessionDir() -> URL {
    precondition(!sessionId.isEmpty)
    let dir = recordingsBaseDir().appendingPathComponent(sessionId, isDirectory: true)
    if !FileManager.default.fileExists(atPath: dir.path) { try? FileManager.default.createDirectory(at: dir, withIntermediateDirectories: true) }
    return dir
  }
  private func ensureSessionDir() throws -> URL { let dir = sessionDir(); if !FileManager.default.fileExists(atPath: dir.path) { try FileManager.default.createDirectory(at: dir, withIntermediateDirectories: true) }; return dir }
  
  
  
  
  // MARK: - FINALIZE (stable) + helpers
  // Paste inside RecorderModule class

  // --- You likely already have this queue/flags. If not, add them.
  private let mergeQueue = DispatchQueue(label: "recorder.merge.queue")
  private var isAppendPaused: Bool = false

  private func setAppendPaused(_ paused: Bool) {
      mergeQueue.sync { self.isAppendPaused = paused }
  }

  @discardableResult
  private func waitUntilExporterIdle(timeout: TimeInterval = 8.0, poll: TimeInterval = 0.05) -> Bool {
      let deadline = Date().addingTimeInterval(timeout)
      while Date() < deadline {
          let busy = mergeQueue.sync { self.isAppendBusy }
          if !busy { return true }
          Thread.sleep(forTimeInterval: poll)
      }
      return false
  }

  // --- tiny helper: is a file "stable" (not still being written)?
  private func isFileStable(_ url: URL, settleMs: Int = 400) -> Bool {
      guard let a1 = try? FileManager.default.attributesOfItem(atPath: url.path),
            let s1 = a1[.size] as? NSNumber
      else { return false }
      usleep(useconds_t(settleMs * 1000))
      guard let a2 = try? FileManager.default.attributesOfItem(atPath: url.path),
            let s2 = a2[.size] as? NSNumber
      else { return false }
      return s1.int64Value == s2.int64Value && s1.int64Value > 0
  }

  // --- safe copy to a snapshot folder so we never read a hot file.
  private func snapshotCopy(of url: URL, to dir: URL) throws -> URL {
      let dst = dir.appendingPathComponent(url.lastPathComponent)
      if FileManager.default.fileExists(atPath: dst.path) {
          try FileManager.default.removeItem(at: dst)
      }
      try FileManager.default.copyItem(at: url, to: dst)
      return dst
  }

  // --- list seg_*.m4a, filter stable, sort by numeric index if present
  private func collectStableTailSegments(in dir: URL) -> [URL] {
      guard let files = try? FileManager.default.contentsOfDirectory(at: dir, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles]) else { return [] }
      let segs = files.filter { $0.lastPathComponent.hasPrefix("seg_") && $0.pathExtension.lowercased() == "m4a" }
      let stable = segs.filter { isFileStable($0, settleMs: 250) }
      func segIndex(_ url: URL) -> Int {
          // seg_0001.m4a → 1 ; fallback 0
          let name = url.deletingPathExtension().lastPathComponent
          if let under = name.split(separator: "_").last, let n = Int(under) { return n }
          return 0
      }
      return stable.sorted { a, b in
          let ia = segIndex(a), ib = segIndex(b)
          if ia != ib { return ia < ib }
          return a.lastPathComponent < b.lastPathComponent
      }
  }

  // --- create a working dir for snapshots + temp export
  // Robust workdir creation: try session dir, then fallback to Caches.
  // Also ensures it's a directory, removes any blocking file,
  // and applies a permissive protection class for temp work.
  private func makeWorkDir(base sessionDir: URL) throws -> URL {
      let fm = FileManager.default

      func ensureDir(at url: URL) throws {
          var isDir: ObjCBool = false
          if fm.fileExists(atPath: url.path, isDirectory: &isDir) {
              if !isDir.boolValue {
                  // A file is sitting where a dir should be → remove it
                  try fm.removeItem(at: url)
              } else {
                  // Already a directory — clear it to avoid stale files
                  try? fm.removeItem(at: url)
              }
          }
          try fm.createDirectory(at: url, withIntermediateDirectories: true, attributes: [
              FileAttributeKey.protectionKey: FileProtectionType.completeUntilFirstUserAuthentication
          ])
      }

      // 1) Try in the session directory
      let primary = sessionDir.appendingPathComponent(".finalize_work", isDirectory: true)
      do {
          try ensureDir(at: primary)
          return primary
      } catch {
          // Only fall back on permission/protection errors
          let nsErr = error as NSError
          let isPerm = nsErr.domain == NSCocoaErrorDomain || nsErr.domain == NSPOSIXErrorDomain
          if !isPerm { throw error }
      }

      // 2) Fallback: Library/Caches/FinalizeWork/<session-id>
      // Extract a stable session identifier from the last path component
      let sessionId = sessionDir.lastPathComponent
      let caches = try fm.url(for: .cachesDirectory, in: .userDomainMask, appropriateFor: nil, create: true)
      let fallbackRoot = caches.appendingPathComponent("FinalizeWork", isDirectory: true)
      let fallback = fallbackRoot.appendingPathComponent(sessionId, isDirectory: true)
      try ensureDir(at: fallbackRoot)
      try ensureDir(at: fallback)
      return fallback
  }

  private func assertSessionDir(_ url: URL) throws -> URL {
      let fm = FileManager.default
      var isDir: ObjCBool = false
      let dir = url.hasDirectoryPath ? url : url.deletingLastPathComponent()
      guard fm.fileExists(atPath: dir.path, isDirectory: &isDir), isDir.boolValue else {
          throw NSError(domain: "FINALIZE", code: -11,
                        userInfo: [NSLocalizedDescriptionKey: "Session directory not found or not a directory: \(dir.path)"])
      }
      return dir
  }



  private let exportQueue = DispatchQueue(label: "recorder.export.queue",
                                         qos: .userInitiated) // or .utility if you prefer

  private func exportComposition(_ composition: AVMutableComposition,
                                 to outURL: URL,
                                 completion: @escaping (Result<URL, Error>) -> Void) {
      try? FileManager.default.removeItem(at: outURL)
      exportQueue.async {
          guard let exporter = AVAssetExportSession(asset: composition,
                                                    presetName: AVAssetExportPresetAppleM4A) else {
              return DispatchQueue.main.async {
                  completion(.failure(NSError(domain: "FINALIZE", code: -4,
                      userInfo: [NSLocalizedDescriptionKey: "Cannot create AVAssetExportSession"])))
              }
          }
          exporter.outputURL = outURL
          exporter.outputFileType = .m4a
          exporter.shouldOptimizeForNetworkUse = false
          exporter.timeRange = CMTimeRange(start: .zero, duration: composition.duration)
          exporter.exportAsynchronously {
              DispatchQueue.main.async {
                  switch exporter.status {
                  case .completed: completion(.success(outURL))
                  case .failed, .cancelled:
                      completion(.failure(exporter.error ?? NSError(domain: "FINALIZE", code: -6,
                          userInfo: [NSLocalizedDescriptionKey: "Export failed"])))
                  default:
                      completion(.failure(NSError(domain: "FINALIZE", code: -7,
                          userInfo: [NSLocalizedDescriptionKey: "Export not completed"])))
                  }
              }
          }
      }
  }

  private func buildAudioComposition(from assets: [AVURLAsset]) throws -> AVMutableComposition {
      let comp = AVMutableComposition()
      guard let audioTrack = comp.addMutableTrack(withMediaType: .audio,
                                                  preferredTrackID: kCMPersistentTrackID_Invalid) else {
          throw NSError(domain: "FINALIZE", code: -2,
                        userInfo: [NSLocalizedDescriptionKey: "Failed to create audio track"])
      }

      var cursor = CMTime.zero
      for asset in assets {
          guard let src = asset.tracks(withMediaType: .audio).first else { continue }

          // Insert full range (you can clamp if needed)
          let timeRange = CMTimeRange(start: .zero, duration: asset.duration)
          do {
              try audioTrack.insertTimeRange(timeRange, of: src, at: cursor)
              cursor = CMTimeAdd(cursor, asset.duration)
          } catch {
              // Skip this segment if it fails to insert
              continue
          }
      }

      // Ensure something actually got inserted
      guard cursor > .zero else {
          throw NSError(domain: "FINALIZE", code: -3,
                        userInfo: [NSLocalizedDescriptionKey: "No audio appended to composition"])
      }
      return comp
  }

  // Loads assets and verifies 'tracks' status == .loaded.
  // Also verifies duration > 0 (common failure case for half-baked files).

  private func loadReadableAudioAssets(from urls: [URL],
                                       timeout: TimeInterval = 6.0) throws -> [AVURLAsset] {
      var good: [AVURLAsset] = []
      for url in urls {
          let asset = AVURLAsset(url: url, options: [
              AVURLAssetPreferPreciseDurationAndTimingKey: true
          ])

          let group = DispatchGroup()
          group.enter()
          var status: AVKeyValueStatus = .unknown

          asset.loadValuesAsynchronously(forKeys: ["tracks"]) {
              status = asset.statusOfValue(forKey: "tracks", error: nil)
              group.leave()
          }

          // Wait with timeout so we don’t hang finalize forever
          let waitResult = group.wait(timeout: .now() + timeout)
          if waitResult == .timedOut { continue }
          guard status == .loaded else { continue }

          // Require at least one audio track
          let audioTracks = asset.tracks(withMediaType: .audio)
          guard !audioTracks.isEmpty else { continue }

          // Duration sanity check (> 0.05s to skip zero/garbage)
          let dur = asset.duration
          guard dur.isNumeric, CMTimeGetSeconds(dur) > 0.05 else { continue }

          good.append(asset)
      }
      return good
  }

  // --- Atomically replace `final.m4a` with a newly exported temp
  private func replaceAtomically(dst: URL, with src: URL) throws {
      let fm = FileManager.default
      if fm.fileExists(atPath: dst.path) {
          let bak = dst.deletingLastPathComponent().appendingPathComponent(".final.bak.m4a")
          try? fm.removeItem(at: bak)
          try fm.moveItem(at: dst, to: bak)
          try fm.moveItem(at: src, to: dst)
          try? fm.removeItem(at: bak)
      } else {
          try fm.moveItem(at: src, to: dst)
      }
  }

  
  
  
 
  // ---------- Helpers (keep if you don’t already have equivalents) ----------

  // Numeric sort; guard on age + stability; filter out ultra-short or unplayable inputs later
  private func collectStableSegments(in dir: URL, minAge: TimeInterval, settleMs: Int) -> [URL] {
      let fm = FileManager.default
      guard let files = try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: [.creationDateKey], options: [.skipsHiddenFiles]) else { return [] }
      let segs = files.filter { $0.lastPathComponent.hasPrefix("seg_") && $0.pathExtension.lowercased() == "m4a" }
      let sorted = segs.sorted { a, b in
          let ia = segIndex(from: a), ib = segIndex(from: b)
          if ia != ib { return ia < ib }
          return a.lastPathComponent < b.lastPathComponent
      }
      let now = Date()
      return sorted.filter { url in
          var okAge = true
          if let attrs = try? fm.attributesOfItem(atPath: url.path),
             let created = attrs[.creationDate] as? Date {
              okAge = now.timeIntervalSince(created) >= minAge
          }
          return okAge && isFileStable(url, settleMs: settleMs)
      }
  }

  private func segIndex(from url: URL) -> Int {
      let stem = url.deletingPathExtension().lastPathComponent // seg_0001
      if let tok = stem.split(separator: "_").last, let n = Int(tok) { return n }
      return 0
  }

 

  // Validate play artifact; retry twice; if still bad, re-encode once and use that
  private func postExportValidateAndMaybeFix(playURL: URL,
                                             fallbackFinal finalURL: URL,
                                             completion: @escaping (URL) -> Void) {
      // light protection on playback file
      try? FileManager.default.setAttributes([.protectionKey: FileProtectionType.completeUntilFirstUserAuthentication],
                                             ofItemAtPath: playURL.path)

      // quick retries to let FS/atoms settle
      if isPlayableFile(playURL) { completion(playURL.absoluteURL); return }
      DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 0.18) {
          if self.isPlayableFile(playURL) { completion(playURL.absoluteURL); return }
          DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 0.22) {
              if self.isPlayableFile(playURL) { completion(playURL.absoluteURL); return }
              // last resort: re-encode once from the newly written final.m4a
              let fixed = playURL.deletingLastPathComponent().appendingPathComponent("final.fixed.m4a")
              self.forceReencodeAppleM4A(input: finalURL, output: fixed) { r in
                  switch r {
                  case .success:
                      try? FileManager.default.removeItem(at: playURL)
                      try? FileManager.default.copyItem(at: fixed, to: playURL)
                      completion(playURL.absoluteURL)
                  case .failure:
                      // fallback to final.m4a if we must
                      completion(finalURL.absoluteURL)
                  }
              }
          }
      }
  }

  private func isPlayableFile(_ url: URL, timeout: TimeInterval = 6.0) -> Bool {
      let asset = AVURLAsset(url: url, options: [AVURLAssetPreferPreciseDurationAndTimingKey: true])
      let keys = ["tracks","playable","duration"]
      let g = DispatchGroup(); g.enter()
      asset.loadValuesAsynchronously(forKeys: keys) { g.leave() }
      guard g.wait(timeout: .now() + timeout) != .timedOut else { return false }
      for k in keys {
          var e: NSError?
          if asset.statusOfValue(forKey: k, error: &e) != .loaded || e != nil { return false }
      }
      guard asset.isPlayable, asset.duration.isValid, asset.duration.seconds > 0.02 else { return false }
      return !asset.tracks(withMediaType: .audio).isEmpty
  }

  private func forceReencodeAppleM4A(input: URL, output: URL, completion: @escaping (Result<URL,Error>) -> Void) {
      try? FileManager.default.removeItem(at: output)
      let asset = AVURLAsset(url: input, options: [AVURLAssetPreferPreciseDurationAndTimingKey:true])
      guard let aTrack = asset.tracks(withMediaType: .audio).first else {
          completion(.failure(NSError(domain: "FINALIZE", code: -21, userInfo: [NSLocalizedDescriptionKey:"No audio track"])))
          return
      }
      let comp = AVMutableComposition()
      guard let dst = comp.addMutableTrack(withMediaType: .audio, preferredTrackID: kCMPersistentTrackID_Invalid) else {
          completion(.failure(NSError(domain: "FINALIZE", code: -22, userInfo: [NSLocalizedDescriptionKey:"Track create failed"])))
          return
      }
      do {
          try dst.insertTimeRange(CMTimeRange(start: .zero, duration: asset.duration), of: aTrack, at: .zero)
      } catch { completion(.failure(error)); return }

      guard let exporter = AVAssetExportSession(asset: comp, presetName: AVAssetExportPresetAppleM4A) else {
          completion(.failure(NSError(domain: "FINALIZE", code: -23, userInfo: [NSLocalizedDescriptionKey:"No exporter"])))
          return
      }
      exporter.outputURL = output
      exporter.outputFileType = .m4a
      exporter.shouldOptimizeForNetworkUse = false
      exporter.exportAsynchronously {
          if exporter.status == .completed { completion(.success(output)) }
          else { completion(.failure(exporter.error ?? NSError(domain: "FINALIZE", code: -24, userInfo: [NSLocalizedDescriptionKey:"Reencode failed"]))) }
      }
  }

  // ===== END DROP-IN =====
  // STRICT: never skip; wait up to perFileTimeout for each asset to be readable.
  // If any file can’t load tracks/playable/duration in time, throw with its name.
  private func loadAudioAssetsStrict(from urls: [URL],
                                     perFileTimeout: TimeInterval = 5.0,
                                     poll: TimeInterval = 0.1) throws -> [AVURLAsset] {
      var assets: [AVURLAsset] = []
      for url in urls {
          let asset = AVURLAsset(url: url, options: [AVURLAssetPreferPreciseDurationAndTimingKey: true])
          let keys = ["tracks","playable","duration"]

          let deadline = Date().addingTimeInterval(perFileTimeout)
          var ok = false
          repeat {
              let g = DispatchGroup(); g.enter()
              asset.loadValuesAsynchronously(forKeys: keys) { g.leave() }
              _ = g.wait(timeout: .now() + poll)
              var allLoaded = true
              for k in keys {
                  var e: NSError?
                  if asset.statusOfValue(forKey: k, error: &e) != .loaded || e != nil { allLoaded = false; break }
              }
              if allLoaded,
                 !asset.tracks(withMediaType: .audio).isEmpty,
                 asset.duration.isValid, asset.duration.seconds > 0.02 {
                  ok = true
                  break
              }
              Thread.sleep(forTimeInterval: poll)
          } while Date() < deadline

          if !ok {
              throw NSError(domain: "FINALIZE",
                            code: -100,
                            userInfo: [NSLocalizedDescriptionKey:
                                        "Source not ready: \(url.lastPathComponent)"])
          }
          assets.append(asset)
      }
      return assets
  }

  // Pretty info for a file
  private func _fileInfo(_ url: URL) -> String {
      let fm = FileManager.default
      var sizeStr = "?"
      var ageStr = "?"
      if let attrs = try? fm.attributesOfItem(atPath: url.path) {
          if let sz = (attrs[.size] as? NSNumber)?.int64Value {
              sizeStr = "\(sz)B"
          }
          if let cDate = attrs[.creationDate] as? Date {
              let sec = max(0, Date().timeIntervalSince(cDate))
              ageStr = String(format: "%.2fs", sec)
          }
      }
      return "\(url.lastPathComponent) [size \(sizeStr), age \(ageStr)]"
  }

  // Numeric seg index (works for seg_0001.m4a – returns 1)
  private func _segIndex(_ url: URL) -> Int {
      let stem = url.deletingPathExtension().lastPathComponent
      if let tok = stem.split(separator: "_").last, let n = Int(tok) { return n }
      return 0
  }

  // Human duration for seconds
  private func _fmtDur(_ s: Double) -> String {
      if s.isNaN || s.isInfinite { return "NaN" }
      if s < 60 { return String(format: "%.2fs", s) }
      let m = Int(s) / 60, sec = s - Double(m*60)
      return String(format: "%dm %.2fs", m, sec)
  }

  

  private func canTryPassthrough(for assets: [AVURLAsset]) -> Bool {
      guard !assets.isEmpty else { return false }

      var refSampleRate: Double?
      var refChannels: UInt32?
      var refSubType: FourCharCode?
      var refLayoutData: Data?

      for asset in assets {
          guard asset.url.pathExtension.lowercased() == "m4a" else { return false }
          guard let track = asset.tracks(withMediaType: .audio).first else { return false }

          // ✅ Avoid conditional CF cast warning:
          guard let anyDesc = track.formatDescriptions.first else { return false }
          let fmtDesc = anyDesc as! CMFormatDescription   // Apple guarantees CMFormatDescription here

          guard CMFormatDescriptionGetMediaType(fmtDesc) == kCMMediaType_Audio else { return false }
          guard let asbdPtr = CMAudioFormatDescriptionGetStreamBasicDescription(fmtDesc) else { return false }
          let asbd = asbdPtr.pointee

          guard asbd.mFormatID == kAudioFormatMPEG4AAC else { return false } // AAC family

          let subType = CMFormatDescriptionGetMediaSubType(fmtDesc)
          if let ref = refSubType, ref != subType { return false }
          refSubType = refSubType ?? subType

          if let rsr = refSampleRate, rsr != asbd.mSampleRate { return false }
          refSampleRate = refSampleRate ?? asbd.mSampleRate

          if let rch = refChannels, rch != asbd.mChannelsPerFrame { return false }
          refChannels = refChannels ?? asbd.mChannelsPerFrame

          var layoutSize: Int = 0
          if let layoutPtr = CMAudioFormatDescriptionGetChannelLayout(fmtDesc, sizeOut: &layoutSize),
             layoutSize > 0 {
              let data = Data(bytes: layoutPtr, count: layoutSize)
              if let ref = refLayoutData, ref != data { return false }
              refLayoutData = refLayoutData ?? data
          }
      }
      return true
  }

  /// Definitive check against the asset you will export (composition).
  /// Confirms that `.passthrough` is compatible and `.m4a` is a supported file type.
  private func canUsePassthrough(for exportAsset: AVAsset) -> Bool {
      let presets = AVAssetExportSession.exportPresets(compatibleWith: exportAsset)
      guard presets.contains(AVAssetExportPresetPassthrough) else { return false }
      guard let probe = AVAssetExportSession(asset: exportAsset, presetName: AVAssetExportPresetPassthrough) else { return false }
      return probe.supportedFileTypes.contains(.m4a)
  }
  
  // STRICT + STABLE + MIN-DURATION FILTER:
  // - waits each URL to become stable & readable
  // - discards segments whose duration < minKeepSeconds
  // - never silently fails: if nothing passes, throws with a clear reason
  private func loadAudioAssetsFiltered(from urls: [URL],
                                       perFileTimeout: TimeInterval = 8.0,
                                       settleMs: Int = 700,
                                       minAge: TimeInterval = 1.5,
                                       poll: TimeInterval = 0.12,
                                       minKeepSeconds: Double = 5.0) throws -> [AVURLAsset] {
      var kept: [AVURLAsset] = []
      let fm = FileManager.default

      for url in urls {
          let deadline = Date().addingTimeInterval(perFileTimeout)
          var readyAsset: AVURLAsset?

          repeat {
              // Age + stability guards
              var ageOK = true
              if let attrs = try? fm.attributesOfItem(atPath: url.path),
                 let created = attrs[.creationDate] as? Date {
                  ageOK = Date().timeIntervalSince(created) >= minAge
              }
              let stable = isFileStable(url, settleMs: settleMs)

              if ageOK && stable {
                  let asset = AVURLAsset(url: url, options: [AVURLAssetPreferPreciseDurationAndTimingKey: true])
                  let keys = ["tracks","playable","duration"]
                  let g = DispatchGroup(); g.enter()
                  asset.loadValuesAsynchronously(forKeys: keys) { g.leave() }
                  _ = g.wait(timeout: .now() + poll)

                  var allLoaded = true
                  for k in keys {
                      var e: NSError?
                      if asset.statusOfValue(forKey: k, error: &e) != .loaded || e != nil { allLoaded = false; break }
                  }

                  if allLoaded,
                     !asset.tracks(withMediaType: .audio).isEmpty,
                     asset.duration.isValid {
                      readyAsset = asset
                      break
                  }
              }
              Thread.sleep(forTimeInterval: poll)
          } while Date() < deadline

          guard let asset = readyAsset else {
              throw NSError(domain: "FINALIZE", code: -100,
                            userInfo: [NSLocalizedDescriptionKey: "Source not ready: \(url.lastPathComponent)"])
          }

          let dur = CMTimeGetSeconds(asset.duration)
          if dur < minKeepSeconds {
              print(String(format: "Finalize: SKIP short %@ — %.2fs < %.2fs",
                           url.lastPathComponent, dur, minKeepSeconds))
              continue
          }

          print(String(format: "Finalize: KEEP %@ — %.2fs ≥ %.2fs",
                       url.lastPathComponent, dur, minKeepSeconds))
          kept.append(asset)
      }

      if kept.isEmpty {
          throw NSError(domain: "FINALIZE", code: -101,
                        userInfo: [NSLocalizedDescriptionKey: "No segments meet minimum duration (\(minKeepSeconds)s)"])
      }
      return kept
  }

  
  
  
  // MARK: - Pre-finalize: stop & settle any active recording
  private func preFinalizeStopAndSettle(settleMs: useconds_t = 800_000) {
      if let r = audioRecorder, r.isRecording {
          r.stop()
          audioRecorder = nil
      }
      // Let file system & atom writes settle a bit
      usleep(settleMs) // ~0.8s
  }
  
  @objc
  func finalizeFromAnyPath(_ anyPath: String,
                           resolve: @escaping RCTPromiseResolveBlock,
                           reject: @escaping RCTPromiseRejectBlock) {
    // Resolve the session directory from the provided path (file://seg_XXXX or a dir)
    let inURL = (URL(string: anyPath)?.scheme == "file")
        ? URL(string: anyPath)!
        : URL(fileURLWithPath: anyPath)
    let sessionDir = inURL.hasDirectoryPath ? inURL : inURL.deletingLastPathComponent()

    let fm = FileManager.default
    guard fm.fileExists(atPath: sessionDir.path) else {
      reject("FINALIZE_ERR","Session directory not found", nil)
      return
    }

    // Output targets
    let finalURL = sessionDir.appendingPathComponent("final.m4a")
    let playURL  = sessionDir.appendingPathComponent("final.play.m4a")

    // 0) Close recorder & pause merges
    preFinalizeStopAndSettle(settleMs: 800_000)
    setAppendPaused(true)
    guard waitUntilExporterIdle(timeout: 10.0, poll: 0.05) else {
      setAppendPaused(false)
      reject("FINALIZE_ERR","Exporter did not become idle", nil)
      return
    }

    // 1) Build a clean snapshot of EVERYTHING we intend to merge.
    //    We deliberately do NOT age-filter on cold start; we only require "stable on disk".
    //    We include:
    //      - existing rolling final.m4a (if stable)
    //      - seg_*.m4a (stable)
    //      - seg_*.caf  (stable)   // legacy sources allowed; exporter will re-encode
    let workDir: URL
    do {
      workDir = try makeWorkDir(base: sessionDir) // writes to Library/Caches fallback if needed
    } catch {
      setAppendPaused(false)
      reject("FINALIZE_ERR","Workdir create failed", error)
      return
    }

    // wipe any old snapshot remnants
    if let olds = try? fm.contentsOfDirectory(at: workDir, includingPropertiesForKeys: nil, options: []) {
      for u in olds { try? fm.removeItem(at: u) }
    }

    // Helper to snapshot-copy a file into workDir with a clear name
    func snapCopy(_ url: URL) -> URL? {
      let name = url.lastPathComponent
      let dst  = workDir.appendingPathComponent(name)
      do {
        if fm.fileExists(atPath: dst.path) { try fm.removeItem(at: dst) }
        try fm.copyItem(at: url, to: dst)
        return dst
      } catch {
      print("Finalize: snapshot copy failed for \(name): \(error.localizedDescription)")
        return nil
      }
    }

    // Gather candidates from the session directory
    let all: [URL]
    do {
      all = try fm.contentsOfDirectory(
        at: sessionDir,
        includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey],
        options: [.skipsHiddenFiles]
      )
    } catch {
      setAppendPaused(false)
      reject("FINALIZE_ERR", "Failed to list session directory", error)
      return
    }

    // 2) Decide sources: final.m4a (if stable), seg_*.m4a, seg_*.caf – all numerically sorted.
    var parts: [URL] = []

    // Include rolling final if present & stable
    let rollingFinal = sessionDir.appendingPathComponent("final.m4a")
    if fm.fileExists(atPath: rollingFinal.path), isFileStable(rollingFinal, settleMs: 250) {
      if let snap = snapCopy(rollingFinal) {
        print("Finalize: KEEP existing final.m4a (snapshot)")
        parts.append(snap)
      }
    }

    // Select segments (m4a + caf)
    // Sort numerically by the XXXX part in seg_XXXX.ext
    func segIndex(_ url: URL) -> Int {
      let stem = url.deletingPathExtension().lastPathComponent
      if stem.hasPrefix("seg_") {
        return Int(stem.replacingOccurrences(of: "seg_", with: "")) ?? Int.max
      }
      return Int.max
    }

    let segsAny = all.filter { url in
      let name = url.lastPathComponent.lowercased()
      return (name.hasPrefix("seg_") && (url.pathExtension.lowercased() == "m4a" || url.pathExtension.lowercased() == "caf"))
    }
    .sorted { segIndex($0) < segIndex($1) }

    // Only require "stable on disk" (no age/size rejection on cold start)
    for s in segsAny {
      if isFileStable(s, settleMs: 250) {
        if let snap = snapCopy(s) {
          parts.append(snap)
          print("Finalize: KEEP \(s.lastPathComponent) (snapshot)")
        } else {
          print("Finalize: SKIP copy-failed \(s.lastPathComponent)")
        }
      } else {
        print("Finalize: SKIP unstable \(s.lastPathComponent)")
      }
    }

    guard !parts.isEmpty else {
      setAppendPaused(false)
      reject("FINALIZE_ERR", "No sources to merge", nil)
      return
    }

    // 3) Merge from snapshots only; we use context 'finalize-all' to apply the 5s keep-rule inside loader.
    let tmpOut = workDir.appendingPathComponent(UUID().uuidString + ".m4a")
    performMerge(outURL: tmpOut, sources: parts, context: "finalize-all") { [weak self] result in
      guard let self = self else { return }
      switch result {
      case .success(let tmpFinal):
        do {
          if fm.fileExists(atPath: finalURL.path) { try fm.removeItem(at: finalURL) }
          try fm.moveItem(at: tmpFinal, to: finalURL)
        } catch {
          self.setAppendPaused(false)
          reject("FINALIZE_ERR", "Move final failed", error)
          return
        }

        // Produce the consumer-safe play artifact and validate/auto-heal (fixes -11829)
        do {
          if fm.fileExists(atPath: playURL.path) { try fm.removeItem(at: playURL) }
          try fm.copyItem(at: finalURL, to: playURL)
        } catch {
          print("Finalize: warn — copy to final.play.m4a failed: \(error.localizedDescription)")
        }

        self.postExportValidateAndMaybeFix(playURL: playURL, fallbackFinal: finalURL) { _ in
          self.setAppendPaused(false)
          resolve(finalURL.absoluteString.hasPrefix("file://") ? finalURL.absoluteString
                                                              : "file://\(finalURL.path)")
        }

      case .failure(let err):
        self.setAppendPaused(false)
        reject("FINALIZE_ERR", (err as NSError).localizedDescription, err)
      }
    }
  }



  
  private func drainAppendQueueIfNeeded(target outURL: URL) {
    guard !isAppendBusy, let next = pendingAppends.first else { return }
    if mergeQueue.sync(execute: { self.isAppendPaused }) { return }

    isAppendBusy = true
    var sources: [URL] = []
    if FileManager.default.fileExists(atPath: outURL.path) { sources.append(outURL) }
    sources.append(next)

    performMerge(outURL: outURL, sources: sources, context: "append") { [weak self] result in
      guard let self = self else { return }
      self.isAppendBusy = false

      switch result {
      case .success(let mergedURL):
        do {
          if FileManager.default.fileExists(atPath: outURL.path) {
            try FileManager.default.removeItem(at: outURL)
          }
          try FileManager.default.moveItem(at: mergedURL, to: outURL)
        } catch { /* log if you want */ }
        // ✅ Dequeue ONLY on success
        if !self.pendingAppends.isEmpty { self.pendingAppends.removeFirst() }

      case .failure(_):
        // ❗ Don’t drop the head blindly—retry once after a short backoff
        appendQueue.asyncAfter(deadline: .now() + 0.25) { [weak self] in
          guard let self = self else { return }
          if !self.mergeQueue.sync(execute: { self.isAppendPaused }) {
            self.drainAppendQueueIfNeeded(target: outURL)
          }
        }
      }

      // Continue draining if not paused
      if !self.mergeQueue.sync(execute: { self.isAppendPaused }) {
        self.drainAppendQueueIfNeeded(target: outURL)
      }
    }
  }


  // MARK: - Core merge (passthrough-first, then re-encode)
  private func performMergeold(outURL: URL,
                            sources: [URL],
                            context: String,
                            completion: @escaping (Result<URL, Error>) -> Void) {
      exportQueue.async { [weak self] in
          guard let self = self else { return }

          do {
              // Strict load with slightly longer patience for finalize-all
              let isFinalAll = (context == "finalize" || context == "finalize-all")
              let perFileTimeout: TimeInterval = isFinalAll ? 12.0 : 8.0   // NEW: give the tail time
              let minKeepSeconds: Double = isFinalAll ? 5.0 : 0.02         // keep-rule for finalize

              let assets = try self.loadAudioAssetsFiltered(
                  from: sources,
                  perFileTimeout: perFileTimeout,
                  settleMs: 700,
                  minAge: 1.5,
                  poll: 0.12,
                  minKeepSeconds: minKeepSeconds
              )

              // Build composition
              let composition = AVMutableComposition()
              guard let compTrack = composition.addMutableTrack(withMediaType: .audio, preferredTrackID: kCMPersistentTrackID_Invalid) else {
                  throw NSError(domain: "MERGE", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to create audio track"])
              }

              var cursor = CMTime.zero
              for (idx, asset) in assets.enumerated() {
                  guard let track = asset.tracks(withMediaType: .audio).first else {
                      throw NSError(domain: "MERGE", code: -2, userInfo: [NSLocalizedDescriptionKey: "No audio track in asset #\(idx)"])
                  }
                  let timeRange = CMTimeRange(start: .zero, duration: asset.duration)
                  try compTrack.insertTimeRange(timeRange, of: track, at: cursor)
                  cursor = CMTimeAdd(cursor, timeRange.duration)
              }

              // Prepare a temp output in a writable work dir
              let workURL = try self.makeWorkDir(base: outURL.deletingLastPathComponent())
                  .appendingPathComponent(UUID().uuidString + ".m4a")

            
            // ✅ Corrected call: only pass preferPassthrough + completion
                       
                        self.exportComposition(composition, to: workURL, completion: completion)

          

          } catch {
              completion(.failure(error))
          }
      }
  }

  
  private func performMerge(outURL: URL,
                            sources: [URL],
                            context: String,
                            completion: @escaping (Result<URL, Error>) -> Void) {
    exportQueue.async { [weak self] in
      guard let self = self else { return }

      // Inner worker to allow a single "drop newest tail" retry if one file is not ready
      func doMerge(with srcs: [URL], allowOneTailDrop: Bool) {
        do {
          let isFinalAll = (context == "finalize" || context == "finalize-all")
          let perFileTimeout: TimeInterval = isFinalAll ? 12.0 : 8.0
          let minKeepSeconds: Double     = isFinalAll ? 5.0  : 0.02

          let assets = try self.loadAudioAssetsFiltered(
            from: srcs,
            perFileTimeout: perFileTimeout,
            settleMs: 700,
            minAge: 1.5,
            poll: 0.12,
            minKeepSeconds: minKeepSeconds
          )

          let composition = AVMutableComposition()
          guard let compTrack = composition.addMutableTrack(withMediaType: .audio,
                                                            preferredTrackID: kCMPersistentTrackID_Invalid) else {
            throw NSError(domain: "MERGE", code: -1,
                          userInfo: [NSLocalizedDescriptionKey: "Failed to create audio track"])
          }

          var cursor = CMTime.zero
          for (idx, asset) in assets.enumerated() {
            guard let track = asset.tracks(withMediaType: .audio).first else {
              throw NSError(domain: "MERGE", code: -2,
                            userInfo: [NSLocalizedDescriptionKey: "No audio track in asset #\(idx)"])
            }
            let tr = CMTimeRange(start: .zero, duration: asset.duration)
            try compTrack.insertTimeRange(tr, of: track, at: cursor)
            cursor = cursor + tr.duration
          }

          let workURL = try self.makeWorkDir(base: outURL.deletingLastPathComponent())
            .appendingPathComponent(UUID().uuidString + ".m4a")

          let preferPT = self.canTryPassthrough(for: assets) && self.canUsePassthrough(for: composition)
          self.exportComposition(composition, to: workURL, completion: completion)

        } catch {
          if allowOneTailDrop,
             let last = srcs.last,
             (error as NSError).localizedDescription.hasPrefix("Source not ready:") {
            print("Merge: tail not ready (\(last.lastPathComponent)) — dropping once and retrying")
            let trimmed = Array(srcs.dropLast())
            if trimmed.isEmpty { completion(.failure(error)) } else { doMerge(with: trimmed, allowOneTailDrop: false) }
            return
          }
          completion(.failure(error))
        }
      }

      doMerge(with: sources, allowOneTailDrop: true)
    }
  }
  private func removeEmptySegments(in dir: URL) {
    let fm = FileManager.default
    guard let items = try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: [.fileSizeKey]) else { return }
    for u in items where u.lastPathComponent.hasPrefix("seg_") && u.pathExtension == "m4a" {
      if let attrs = try? fm.attributesOfItem(atPath: u.path),
         let size = attrs[.size] as? NSNumber, size.intValue == 0 {
        try? fm.removeItem(at: u)
      }
    }
  }
  private func durationOfFile(_ url: URL, completion: @escaping (Double?) -> Void) {
    let asset = AVURLAsset(url: url, options: [AVURLAssetPreferPreciseDurationAndTimingKey: true])
    asset.loadValuesAsynchronously(forKeys: ["duration"]) {
      var err: NSError?
      let status = asset.statusOfValue(forKey: "duration", error: &err)
      guard status == .loaded, err == nil else {
        DispatchQueue.main.async { completion(nil) }
        return
      }
      let d = asset.duration
      let secs = (d.isValid && !d.isIndefinite) ? d.seconds : nil
      DispatchQueue.main.async { completion(secs) }
    }
  }
  
  private func loadStoredSegments() -> [String] {
      (UserDefaults.standard.array(forKey: STORED_SEGMENTS_KEY) as? [String]) ?? []
  }

  private func saveStoredSegments(_ arr: [String]) {
      UserDefaults.standard.set(arr, forKey: STORED_SEGMENTS_KEY)
  }

  private func saveSessionDirIfNeeded(_ dirURL: URL) {
      let dirString = dirURL.absoluteString   // <-- keep as file:// form
      if UserDefaults.standard.string(forKey: STORED_SESSION_DIR_KEY) != dirString {
          UserDefaults.standard.set(dirString, forKey: STORED_SESSION_DIR_KEY)
      }
  }

  /// Accept .m4a, .m4, or .caf (your example shows `.m4`, iOS typically uses `.m4a`)
  private func isAudioSegmentURL(_ url: URL) -> Bool {
      let ext = url.pathExtension.lowercased()
      return ext == "m4a" || ext == "m4" || ext == "caf"
  }

  /// De-dupe and append the **file://** URL string
  private func rememberSegment(url: URL) {
      guard isAudioSegmentURL(url) else { return }
      let fileURLString = url.absoluteString   // <-- store exactly "file:///var/…/seg_0051.m4a"
      persistQueue.async {
          var arr = self.loadStoredSegments()
          if !arr.contains(fileURLString) {
              arr.append(fileURLString)
              // Optional: cap to last N if you like (e.g., last 500)
              // if arr.count > 500 { arr.removeFirst(arr.count - 500) }
              self.saveStoredSegments(arr)
          }
      }
  }
  
  @objc
  func clearStoredSegments(_ resolve: @escaping RCTPromiseResolveBlock,
                          reject: @escaping RCTPromiseRejectBlock) {
      
      persistQueue.async {
          do {
              // Clear the stored segments array
              self.saveStoredSegments([])
              
              // Clear the session directory reference
              UserDefaults.standard.removeObject(forKey: self.STORED_SESSION_DIR_KEY)
              
              // Optional: Also clear duration data if you want to reset everything
              let userDefaults = UserDefaults.standard
              let allKeys = userDefaults.dictionaryRepresentation().keys
              
              // Remove all duration storage keys (they start with "RecordingSession_")
              for key in allKeys {
                  if key.hasPrefix("RecordingSession_") && key.hasSuffix("_Duration") {
                      userDefaults.removeObject(forKey: key)
                  }
              }
              
              // Force UserDefaults to save immediately
              userDefaults.synchronize()
              
              DispatchQueue.main.async {
                  resolve([
                      "cleared": true,
                      "message": "All stored segments and session data cleared successfully"
                  ])
              }
              
          } catch {
              DispatchQueue.main.async {
                  reject("CLEAR_ERR", "Failed to clear stored segments: \(error.localizedDescription)", error)
              }
          }
      }
  }

}

