const path = require('path');
// const { getDefaultConfig } = require('expo/metro-config');

// const config = getDefaultConfig(__dirname);
const { getSentryExpoConfig } = require("@sentry/react-native/metro");
const config = getSentryExpoConfig(__dirname);

// ✅ Set absolute path for alias
config.resolver.alias = {
  ...config.resolver.alias,
  '@': path.resolve(__dirname, './'),
};

module.exports = config;
