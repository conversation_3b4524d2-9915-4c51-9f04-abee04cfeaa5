import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  Dimensions, // Add this

} from "react-native";
import Checkbox from "expo-checkbox";
// import { LogIn, Mail, Lock } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useResponsive } from "src/hooks/useResponsive";
import LoginRightPane from "src/components/login-right-pane/LoginRightPane";
import { SafeAreaView } from "react-native-safe-area-context";
import { globalStyles } from "src/styles";
import { StatusBar } from "expo-status-bar";
import theme from "src/theme";
import { useDispatch } from "react-redux";
import { login } from "@/src/redux/slices/authSlice";
import { validateEmail } from "src/utils/ProfileUtils";
import { getProfileData, loginApi, loginApiV2 } from "src/screens/login/api";
import { Role } from "src/enums/Role";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { setPageTitle } from "@/src/utils/GeneralUtils";
// import { getSecureItem, setSecureItem } from "@/src/utils/cryptoHelper";
// import { useLogin } from "@/src/context/LoginContext";
import Feather from '@expo/vector-icons/Feather';
import { Octicons } from "@expo/vector-icons";
import { BUILDING_ICON } from "@/assets/images";
import { black } from "react-native-paper/lib/typescript/styles/themes/v2/colors";
import { getSecureItem, removeSecureItem, setSecureItem } from "@/src/utils/cryptoHelper";
import { fs } from "@/src/utils/ScreenUtils";
import { MessageModal } from "@/src/utils/MessageModal";
import * as Sentry from '@sentry/react-native';

export default function LoginForm({ navigation }: { navigation: any }) {

  useEffect(() => {
    setTimeout(() => {
      setPageTitle('Login | Scribble');
    }, 100)
  }, [])
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [remember, setRemember] = useState(false);
  const [isDisabled, setDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [org, setOrg] = useState("");
  const { isTabletOrMobileDevice } = useResponsive();

  const dispatch = useDispatch();

  const styles = loginScreenStyles(isTabletOrMobileDevice);

  const [showMessageModal, setShowMessageModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [messageType, setMessageType] = useState("error");

  const onModalCancel = () => setShowMessageModal(false);

  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);


  // const {setLoginDetails} =useLogin()


  useEffect(() => {
    setErrorMessage("");
    setDisabled(!validateEmail(email) || password.trim().length < 4);
  }, [email, password]);

  // Check if we're already logged in
  // useEffect(() => {
  //   const checkExistingAuth = async () => {
  //     try {
  //       // const token = await getSecureItem("authToken");
  //       //         const email = await getSecureItem("email");

  //       // if (token && userRole) {
  //       //   // We're already logged in, redirect to the appropriate screen
  //       //   if (userRole === Role.USER_ADMIN) {
  //       //     if (isTabletOrMobileDevice) {
  //       //       navigation.replace("AccessRestrictionScreen");
  //       //     } else {
  //       //       navigation.replace("AdminDashboard");
  //       //     }
  //       //   } else {
  //       //     if (!isTabletOrMobileDevice) {
  //       //       navigation.replace("AccessRestrictionScreen");
  //       //     } else {
  //       //       navigation.replace("Dashboard");
  //       //     }
  //       //   }
  //       // }
  //       const org = await getSecureItem("org");

  //       if (org && org !== "") {
  //         setOrg(org as string)
  //       } else {
  //         navigation.replace("Login");
  //       }
  //     } catch (error) {
  //       console.log("Error checking auth status:", error);
  //       // Continue with login screen if there's an error
  //     }
  //   };

  //   checkExistingAuth();
  // }, []);

  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    const checkExistingAuth = async () => {
      try {
        const org = await getSecureItem("org");

        if (org && org !== "") {
          setOrg(org as string);
        } else {
          navigation.replace("Login");
          return;
        }
      } catch (error) {
        console.log("Error checking auth status:", error);
        navigation.replace("Login");
        return;
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkExistingAuth();
  }, []);

  // Add this early return to prevent rendering during auth check
  if (isCheckingAuth) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#4C51BF" />
      </View>
    );
  }




  const navigateToHome = async (response) => {
    const { email, role, roles, isFirstLogin, firstName, lastName, staffId, discipline, disciplineId, phone, dateOfBirth } = response;
    // console.log("dateOfBirth :",dateOfBirth)
    try {
      await setSecureItem('firstName', firstName);
      await setSecureItem("lastName", lastName);
      await setSecureItem('staffId', staffId || "");
      await setSecureItem("email", email)

      await setSecureItem("phone", phone);
      await setSecureItem('dateOfBirth', dateOfBirth);
      //await removeSecureItem("recording");

      if (Platform.OS === 'web') {
        await setSecureItem('discipline', disciplineId);
        await setSecureItem("userRole", roles.length > 0 ? roles[0] : null);
      } else {
        await setSecureItem('discipline', discipline);
        await setSecureItem("userRole", role);
      }

      dispatch(login({ user: response, role }));
      let user_role = Platform.OS === 'web' ? roles[0] : role;
      // Navigate to the appropriate screen
      if (isFirstLogin) {
        navigation.replace("ResetPassword");
      } else if (user_role === Role.USER_ADMIN) {
        if (isTabletOrMobileDevice) {
          navigation.replace("AccessRestrictionScreen");
        } else {
          navigation.replace("AdminDashboard");
        }
      } else {
        if (!isTabletOrMobileDevice) {
          navigation.replace("AccessRestrictionScreen");
        } else {
          navigation.replace("Dashboard");
        }
      }
    } catch (error) {
      console.error("Storage error during login:", error);
      setErrorMessage("Error during login process. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };
  const onBackClick = () => {
    navigation.replace('Login');
  };

  const handleSubmit = async () => {
    Keyboard.dismiss();
    setIsLoading(true);
    // console.log("this is hit", email, password)
    try {
      if (Platform.OS === "web") {
        let details = await loginApi({ email, password })
        if (details.status.toLowerCase() === "ok") {
          await setSecureItem('authToken', details.data.accessToken);
          await setSecureItem("refreshToken", details.data.refreshToken)
          navigateToHome(details.data);
        } else {
          setErrorMessage("Incorrect email or password");
          setIsLoading(false);
          setDisabled(true);
        }
      } else {
        let details = await loginApiV2({ email, password })

        console.log("Login Response :" ,JSON.stringify(details))
        await setSecureItem('authToken', details.accessToken);
        await setSecureItem('refreshToken', details.refreshToken);
        let profiledata = await getProfileData()

        navigateToHome(profiledata);

      }
    } catch (e) {
      console.log(e);
      let errorMessage = e;
      setErrorMessage(errorMessage);
      // setModalMessage(errorMessage);
      // setMessageType("error");
      // setShowMessageModal(true);
    } finally {
      setIsLoading(false);
      setDisabled(true);
    }
    // loginApi({ email, password })
    //   .then((result) => {
    //     // console.log("this is hit--->", email, password)

    //     if (result?.status.toLowerCase() === "ok") {
    //       navigateToHome(result.data);
    //     } else {
    //       setErrorMessage("Incorrect email or password");
    //       setIsLoading(false);
    //       setDisabled(true);
    //     }
    //   })
    //   .catch((error) => {
    //     console.log(error);
    //     setErrorMessage("Incorrect email or password");
    //     setIsLoading(false);
    //     setDisabled(true);
    //   });
  };

  const onForgotClick = () => {
    navigation.replace("ForgotPassword");
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleEmailContainerPress = () => {
    if (Platform.OS === 'web' && emailInputRef.current) {
      emailInputRef.current.focus();
    }
  };

  const handlePasswordContainerPress = () => {
    if (Platform.OS === 'web' && passwordInputRef.current) {
      passwordInputRef.current.focus();
    }
  };


  return (
    <SafeAreaView style={globalStyles.flex1}>
      {/* <StatusBar style="dark" /> */}
      <MessageModal
        visible={showMessageModal}
        onCancel={onModalCancel}
        message={modalMessage}
        type={messageType}
      />
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.outerContainer}
          keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
        >
          <LinearGradient
            colors={["#e0e7ff", "#ffffff", "#f3e8ff"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientBG}
          >
            <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
              {/* <ChevronLeft color="black" size={20} /> */}
              <Feather name="chevron-left" size={20} color="black" />

            </TouchableOpacity>
            {/* Main container that can hold both left form + right pane if not mobile */}
            <ScrollView
              contentContainerStyle={styles.scrollContainer}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            >

              <View
                style={[
                  styles.container,
                  isTabletOrMobileDevice
                    ? styles.containerMobile
                    : styles.containerDesktop,
                ]}
              >
                {/* Left (login card) */}
                <View style={styles.loginCard}>
                  <View style={styles.loginContainer}>
                    <View style={styles.iconContainer}>

                      <Image
                        source={require("assets/images/icon.png")}
                        style={styles.logo}
                      />
                    </View>

                    <View style={styles.headerContainer}>
                      <Text allowFontScaling={false} style={styles.title}>Welcome to Goodly AI</Text>
                      <Text allowFontScaling={false} style={styles.subtitle}>
                        {/* Elevate Patient Care with AI */}
                        Patients, Not Paperwork
                      </Text>
                    </View>


                    <View style={[styles.inputContainer, { opacity: 0.5, color: 'black' }]}>
                      <View style={styles.iconWrapper}>
                        <Image source={BUILDING_ICON} style={{ width: 20, height: 20 }} />
                      </View>
                      <TextInput
                        allowFontScaling={false}
                        style={styles.input}
                        // placeholder="Enter your organization name"
                        editable={false}
                        placeholderTextColor="grey"
                        value={org}
                        onChangeText={setOrg}
                        autoCapitalize="none"
                        autoCorrect={false}
                      />
                    </View>
                    {/* Email input */}
                    <TouchableOpacity
                      activeOpacity={1}
                      onPress={handleEmailContainerPress}
                      style={styles.inputContainer}
                    >
                      <View style={styles.iconWrapper}>
                        {/* <Mail color="#9CA3AF" size={20} /> */}
                        <Feather name="mail" size={20} color="#9CA3AF" />
                      </View>
                      <TextInput
                        ref={emailInputRef}
                        allowFontScaling={false}
                        style={[
                          styles.input,
                          Platform.OS === 'web' && styles.webInput // Add this line
                        ]}
                        placeholder="Email address"
                        placeholderTextColor="grey"
                        value={email}
                        onChangeText={setEmail}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        autoFocus={Platform.OS === 'web'}
                      />
                    </TouchableOpacity>

                    {/* Password input */}
                    <TouchableOpacity
                      activeOpacity={1}
                      onPress={handlePasswordContainerPress}
                      style={styles.inputContainer}
                    >
                      <View style={styles.iconWrapper}>
                        {/* <Lock color="#9CA3AF" size={20} /> */}
                        <Feather name="lock" size={20} color="#9CA3AF" />
                      </View>
                      <TextInput
                        ref={passwordInputRef}
                        allowFontScaling={false}
                        style={[
                          styles.input,
                          Platform.OS === 'web' && styles.webInput // Add this line
                        ]}
                        placeholder="Password"
                        placeholderTextColor="grey"
                        value={password}
                        onChangeText={setPassword}
                        secureTextEntry
                        selectTextOnFocus={Platform.OS === 'web'}
                      />
                    </TouchableOpacity>

                    {/* Remember me / Forgot */}
                    <View style={styles.optionsRow}>
                      <View style={styles.rememberMe}>
                        <Checkbox
                          value={remember}
                          onValueChange={setRemember}
                          color={remember ? "#4f46e5" : "grey"}
                          style={styles.checkbox}
                        />
                        <Text allowFontScaling={false} style={styles.rememberMeText}>Remember me</Text>
                      </View>
                    </View>

                    <TouchableOpacity
                      style={styles.forgotTextContainer}
                      onPress={onForgotClick}
                    >
                      <Text allowFontScaling={false} style={styles.forgotText}>Forgot Password?</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.buttonContainer,
                        isDisabled && styles.disabledButton,
                      ]}
                      activeOpacity={isDisabled ? 0.5 : 0.9}
                      onPress={() => !isLoading && handleSubmit()}
                      disabled={isDisabled}
                    >
                      <LinearGradient
                        colors={
                          isDisabled
                            ? ["#A0A0A0", "#C0C0C0"]
                            : ["#4C51BF", "#6B46C1"]
                        }
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.buttonGradient}
                      >
                        <View style={styles.buttonContent}>
                          {isLoading ? (
                            <ActivityIndicator
                              size={isTabletOrMobileDevice ? "small" : "large"}
                              color="#FFFFFF"
                            />
                          ) : (
                            <Text allowFontScaling={false} id="login-sign-in-button" style={styles.buttonText}>Sign In</Text>
                          )}
                        </View>
                      </LinearGradient>
                    </TouchableOpacity>
                    {errorMessage && (
                      <View style={styles.errorContainer}>
                        <Text
                          style={[
                            styles.errorText,
                            isTabletOrMobileDevice
                              ? styles.mobileError
                              : styles.desktopError,
                          ]}
                        >
                          {errorMessage}
                        </Text>
                      </View>
                    )}
                     {/* <TouchableOpacity
                  style={styles.sentryTestButton}
                  onPress={() => {
                    Sentry.captureException(new Error('Testing Sentry error logging'));
                  }}
                >
                  <Text allowFontScaling={false} style={styles.sentryTestButtonText}>
                    Test Sentry
                  </Text>
                </TouchableOpacity>  */}
                  </View>
                  {!isTabletOrMobileDevice && <LoginRightPane />}
                </View>
              </View>
            </ScrollView>


          </LinearGradient>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeAreaView >
  );
}

const loginScreenStyles = (isMobile: boolean) =>
  StyleSheet.create({
    outerContainer: {
      flex: 1,
    },
    gradientBG: {
      flex: 1,
      padding: 16,
      justifyContent: "center",
    },
    container: {
      backgroundColor: "transparent",
      borderRadius: 16,
      overflow: "hidden",
      paddingTop: 10,
      paddingBottom: 10,
    },
    // Layout differences for mobile vs. desktop
    containerMobile: {
      flexDirection: "column",
      alignItems: "center",
    },
    containerDesktop: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
    },
    // The login card (left side)
    loginCard: {
      width: isMobile ? "95%" : "70%",
      borderRadius: 16,
      shadowColor: "#000",
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      display: "flex",
      flexDirection: "row",

    },
    loginContainer: {
      alignSelf: "center",
      maxWidth: isMobile ? "100%" : "100%",
      width: isMobile ? "100%" : "50%",
      padding: isMobile ? 20 : 100,
      paddingVertical: isMobile ? 20 : 30,
      paddingBottom: isMobile ? 30 : 40,
      backgroundColor: "#FFFFFF",
      borderBottomRightRadius: isMobile ? 16 : 0,
      borderTopRightRadius: isMobile ? 16 : 0,
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    },
    iconContainer: {
      display: "flex",
      justifyContent: "center",
      marginBottom: 20,
      alignItems: "center",
      // backgroundColor:"red"
    },
    logo: {
      width: isMobile ? 70 : 80,
      height: isMobile ? 70 : 80,
      resizeMode: "contain",
    },
    headerContainer: {
      marginBottom: isMobile ? 24 : 36,
      alignItems: "center",
    },
    title: {
      fontSize: isMobile ? 18 : 20,
      fontFamily: "Poppins_600SemiBold",
      color: "#1F2937",
      marginBottom: isMobile ? 6 : 12,
    },
    subtitle: {
      fontSize: 14,
      fontFamily: "Poppins_400Regular",
      color: "#4B5563",
    },
    inputContainer: {
      position: "relative",
      marginBottom: 16,
      flexDirection: "row",
      alignItems: "center",
      width: "100%",
    },
    iconWrapper: {
      position: "absolute",
      left: 14,
      zIndex: 1,
      opacity: 0.7,
    },
    input: {
      flex: 1,
      paddingVertical: 14,
      paddingLeft: 42,
      paddingRight: 16,
      borderWidth: 1,
      borderColor: "#E5E7EB",
      borderRadius: 8,
      fontSize: 16,
      color: "#14335dff",
      backgroundColor: "white",
    },
    optionsRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 8,
      marginBottom: 24,
    },
    checkbox: {
      borderRadius: 8,
    },
    rememberMe: {
      flexDirection: "row",
      alignItems: "center",
    },
    rememberMeText: {
      marginLeft: 8,
      fontSize: 14,
      fontFamily: "Poppins_400Regular",
      color: "#4B5563",
    },
    forgotTextContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 20,
    },
    forgotText: {
      fontSize: isMobile ? 14 : 16,
      fontFamily: "Poppins_400Regular",
      color: "#4C51BF",
    },
    buttonContainer: {
      height: isMobile ? 48 : 56,
    },
    disabledButton: {
      cursor: "not-allowed",
    },
    buttonGradient: {
      borderRadius: 12,
      paddingVertical: 12,
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: isMobile ? 24 : 32,
    },
    buttonText: {
      marginLeft: 8,
      color: "#FFFFFF",
      fontSize: isMobile ? 14 : 16,
      fontFamily: "Poppins_600SemiBold",
    },
    errorText: {
      color: "#DC2626",
      fontSize: 14,
      textAlign: "center",
      fontFamily: "Poppins_400Regular",
    },
    mobileError: {
      fontSize: 14,
    },
    desktopError: {
      fontSize: 16,
    },
    errorContainer: {
      backgroundColor: "#FEF2F2",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: isMobile ? 8 : 12,
      paddingHorizontal: 30,
      marginTop: 16,
      borderRadius: 4,
    },
    backContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 24,
      backgroundColor: "red"
    },
    backButton: {
      gap: 8,
      position: 'absolute',
      top: isMobile ? 16 : 32,
      left: isMobile ? 16 : 32,
      zIndex: 10,
      width: 48,
      height: 48,
      backgroundColor: isMobile ? '#FFFFFF' : '#f9fafb',
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: "center",
      minHeight: Dimensions.get('window').height - 100,
    },
    webInput: {
      outlineStyle: 'none',
      outlineWidth: 0,
      outlineColor: 'transparent',
    },
    sentryTestButton: {
      marginTop: 16,
      paddingVertical: 8,
      paddingHorizontal: 16,
      backgroundColor: '#DC2626',
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    sentryTestButtonText: {
      color: '#FFFFFF',
      fontSize: isMobile ? 12 : 14,
      fontFamily: 'Poppins_500Medium',
    },
  });
