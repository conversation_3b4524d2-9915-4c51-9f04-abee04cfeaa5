import React, { useCallback, useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Animated,
  ActivityIndicator,
  Platform,
  ScrollView
} from "react-native";
// import { UserCheck, FileText, ChevronLeft } from "lucide-react-native";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { globalStyles } from "src/styles";
import theme from "src/theme";
import GreetingHeader from "src/components/greeting-header/GreetingHeader";
import VisitItem from "src/features/visits/components/visit-item/VisitItem";
import { getVisitList } from "src/features/visits/components/upcoming-visit/visit-api";
import { setPageTitle } from "@/src/utils/GeneralUtils";

const OpenVisits = ({ route }) => {

  const visit_type = route.params?.type || "New";
  const screen =route.params?.screen

  console.log(screen)
  // console.log("Click type:" + visit_type);
  let pageTitle = "Visits | New"
  if (visit_type == "To be reviewed") {
    pageTitle = "Visits | In-Progress";
  } else if (visit_type == "Past Due") {
    pageTitle = "Visits | Past Due";
  } else if (visit_type == "Completed") {
    pageTitle = "Visits | Completed"
  }
  setPageTitle(pageTitle);

  const navigation = useNavigation();
  const [visits, setVisits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const isWeb = Platform.OS === 'web';
  const containerRef = useRef(null);
  const scrollTimeout = useRef(null);

  const onClickVisit = (visit) => {
    // navigation.navigate("VisitDetails", { visit });
    navigation.navigate("VisitScreen", { visit })
  };

  const limit = 10;

  const fetchVisits = async (pageNumber = 1) => {
    if (loadingMore || !hasMore) return;

    try {
      if (pageNumber === 1) setLoading(true);
      else setLoadingMore(true);

      const result = await getVisitList({ limit: limit, page: pageNumber, status: visit_type,screen:screen });

       //console.log("visit list==========",result );

      if (result.data.length > 0) {
        setVisits((prev) => (pageNumber === 1 ? result.data : [...prev, ...result.data]));
        setPage(pageNumber + 1);
        if (result.data.length < limit || result.total_records_available <= limit * pageNumber) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };



  useFocusEffect(
    useCallback(() => {
      setHasMore(true);
      setPage(1);
      fetchVisits(1); // Fetch initial data
    }, [])
  );

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      setHasMore(true);
      fetchVisits(page);
    }
  };

  const handleWebScroll = (event) => {
    if (scrollTimeout.current) clearTimeout(scrollTimeout.current);

    scrollTimeout.current = setTimeout(() => {
      if (event.target) {
        const { scrollTop, scrollHeight, clientHeight } = event.target;
        console.log(`ScrollTop: ${scrollTop}, ScrollHeight: ${scrollHeight}, ClientHeight: ${clientHeight}`);
        if (scrollHeight - scrollTop <= clientHeight + 20) {
          handleLoadMore();
        }
      }
    }, 100);
  };
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleWebScroll);
    }

    // Check for null before removing the event listener
    return () => {
      if (container) {
        container.removeEventListener('scroll', handleWebScroll);
      }
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [page, loadingMore, hasMore]);

  const refreshPage = useCallback((visitId:string)=>{
    setVisits(prevVisits => prevVisits.filter(visit => visit._id !== visitId));
  },[])

  // Render component for web platforms
  const renderWebContent = () => {
    return (
      <div ref={containerRef}
        style={{
          overflowY: 'auto',
          height: '70vh',
          padding: '10px',
        }}
      >
        {visits.map(item => (
          <VisitItem key={item?._id} visit={item} onClickVisit={onClickVisit} type={visit_type} onRefresh={refreshPage}/>
        ))}

        {loadingMore && (
          <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10, marginBottom: 10 }} />
        )}

      </div>
    );
  };

  // Render component for native platforms
  const renderNativeContent = () => {
    return (
      <FlatList
        style={{ paddingHorizontal: 16, marginTop: 16 }}
        data={visits}
        keyExtractor={(item) => item?._id}
        renderItem={({ item }) => (
          <VisitItem key={item?._id} visit={item} onClickVisit={onClickVisit} type={visit_type} onRefresh={refreshPage}/>
        )}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={
          loadingMore ? (
            <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10 }} />
          ) : null
        }
      />
    );
  };

  return (
    <View style={styles.container}>
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
      ) : visits.length > 0 ? (
        isWeb ? renderWebContent() : renderNativeContent()
      ) : (
        <View style={styles.blankTextContainer}>
          <Text style={styles.blankText}>No visits available</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    // paddingVertical: 10,
  },
  blankTextContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  blankText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 20,
    fontFamily: 'Poppins_500Medium'
  },
  loadMoreButton: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 10,
    alignItems: 'center',
    marginVertical: 20,
  },
  loadMoreText: {
    color: '#0000ff',
    fontWeight: 'bold',
  },
  header: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 20,
    color: theme.colors.text,
    paddingHorizontal: 16,
    textAlign: "center",
    marginBottom: 16,
  },
  tabContainer: {
    flexDirection: "row",
    marginVertical: 16,
    gap: 8,
    paddingHorizontal: 16,
  },
  tag: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.colors.secondary,
    alignItems: "center",
    justifyContent: "center",
    height: 36,
  },
  tagText: {
    color: theme.colors.secondary,
    fontSize: 13,
    fontFamily: "Poppins_400Regular",
  },
  selectedTag: {
    backgroundColor: theme.colors.secondary,
  },
  selectedTagText: {
    color: "#fff",
  },
  notificationCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    marginBottom: 12,
    borderRadius: 10,
    backgroundColor: "#F9FAFB",
  },
  notificationText: {
    marginLeft: 12,
    flex: 1,
  },
  notificationTitle: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: theme.colors.text,
  },
  notificationDesc: {
    fontSize: 13,
    color: "#6B7280",
  },
  notificationTime: {
    fontSize: 12,
    color: "#9CA3AF",
    marginTop: 4,
  },
  unRead: {
    backgroundColor: "#EFF6FF",
  },
  indicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
    backgroundColor: "#f26649",
  },
  backButton: {
    gap: 8,
    position: "absolute",
    top: 16,
    left: 16,
    zIndex: 10,
    width: 40,
    height: 40,
    backgroundColor: "#FFFFFF",
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
});

export default OpenVisits;