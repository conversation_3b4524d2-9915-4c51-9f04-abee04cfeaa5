import React, { useState, useRef, useEffect, useCallback, memo } from "react";
import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  Animated,
  TouchableOpacity,
  TextInput,
  Platform,
  Linking,
  ActivityIndicator,
  Modal,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { LinearGradient } from "expo-linear-gradient";
// import {

//   Clock,
//   MapPin,
//   ChevronLeft,
//   CalendarDays,
//   ClipboardList,
//   Eye,
//   Briefcase,
// } from "lucide-react-native";
import { globalStyles } from "src/styles";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import VisitItem from "src/features/visits/components/visit-item/VisitItem";
import theme from "src/theme";
import { getInitials } from "src/utils/ProfileUtils";
import { SafeAreaView } from "react-native-safe-area-context";
import { fs, screenHeight, screenWidth } from "src/utils/ScreenUtils";
import GreetingHeader from "src/components/greeting-header/GreetingHeader";
import UpcomingVisit from "src/features/visits/components/upcoming-visit/UpcomingVisit";
import { visitDetails } from "src/features/visits/components/upcoming-visit/visit-api";

import { VisitStatusPieChart } from "../components/DailyReportPie";

import { setPageTitle } from "@/src/utils/GeneralUtils";
import { useDispatch } from "react-redux";
import { getSecureItem } from "@/src/utils/cryptoHelper";
import { showRecorder } from "@/src/redux/slices/audioRecorderSlice";

export default function ClinicianDashboard({ route }) {

  const navigation = useNavigation();
  const dispatch = useDispatch();

  // useEffect(()=>{
  //   setTimeout(()=>{
  setPageTitle('Home | Scribble');
  //   },100)
  // },[])
  const onClickOpenVisit = () => {
    //console.log('okkkkkkkkkkkkkkk');
    // navigation.navigate("OpenVisits");
    navigation.navigate("AllVisits", { initialTab: "New" });
  };

  const onClickToBeReviewed = () => {
    // navigation.navigate("ToBeReviewed");
    navigation.navigate("AllVisits", { initialTab: "In Progress" });
  };

  const onClickPastDueDate = () => {
    // navigation.navigate("PastDueDate");
    navigation.navigate("AllVisits", { initialTab: "Past Due" });
  };

  const onClickVisit = (visit) => {
    navigation.navigate("VisitDetails", { visit });
  };

  // const onViewAllVisits = () => {
  //   navigation.navigate('VisitDetails', { visit });
  // };

  const onClickStartVisit = (visit) => {
    navigation.navigate("Recorder", { visit });
  };
  const [todaysVisit, setTodaysVisit] = useState({ "totalVisits": 0, "newVisits": 0, "inProgressVisits": 0, "completedVisits": 0 })
  const [loading, setLoading] = useState(true);

  const [visible, setVisible] = useState(true);

  // const { showPopup, message, hideNotification } = useNotification();
  useEffect(() => {
    const checkRecording = async () => {
      const storedRecording = await getSecureItem("recording");

      console.log("Stored recording in dashboard:", storedRecording);
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);
         if(recordingData.email == await getSecureItem("email")){
        console.log("Assesment Id:", recordingData.assesmentId);
        dispatch(showRecorder({
          expanded: false,
          assesId: recordingData.assesmentId
        }));
      }
      }

    }
    checkRecording()
  }, [])
  const onClickOpenMap = () => {
    // const latitude = 40.74958102694104;
    // const longitude = -73.98537418664596;
    // const url = `https://www.google.com/maps?q=${latitude},${longitude}`;
    //
    // Linking.openURL(url);
  };

  // const BottomPopup = memo(({ visible, onClose }) => {
  //   const slideAnim = useRef(new Animated.Value(100)).current;

  //   useEffect(() => {
  //     if (visible) {
  //       Animated.timing(slideAnim, {
  //         toValue: 0,
  //         duration: 300,
  //         useNativeDriver: true,
  //       }).start();
  //     } else {
  //       Animated.timing(slideAnim, {
  //         toValue: 100,
  //         duration: 300,
  //         useNativeDriver: true,
  //       }).start();
  //     }
  //   }, [visible]);

  //   if (!visible) return null;

  //   return (
  //     <Modal transparent animationType="none" visible={visible}>
  //       <View style={styles.overlay}>
  //         <Animated.View
  //           style={[
  //             styles.popupContainer,
  //             { transform: [{ translateY: slideAnim }] },
  //           ]}
  //         >
  //           <Ionicons name="sparkles" size={24} color="#ffffff" />
  //           <Text style={styles.message}>
  //             Hold tight, Scribble is processing your request
  //           </Text>
  //           <TouchableOpacity onPress={onClose} style={{paddingLeft:8,paddingRight:8,paddingTop:8,paddingBottom:8}}>
  //             <Ionicons name="close" size={20} color="#ffffff" />
  //           </TouchableOpacity>
  //         </Animated.View>
  //       </View>
  //     </Modal>
  //   );
  // }, (prevProps, nextProps) => prevProps.visible === nextProps.visible);

  // const renderHeader = () => {
  //   return (
  //     <View style={styles.header}>
  //       <View style={styles.profileBox}>
  //         <Text style={styles.profileInitials}>
  //           {getInitials(clinicianName)}
  //         </Text>
  //       </View>
  //       <Text style={styles.headerTitle}>{"Dashboard"}</Text>
  //     </View>
  //   );
  // };

  const renderWidgets = () => {

    const loadData = () => {
      setLoading(true);
      visitDetails()
        .then((result) => {
          // console.log(JSON.stringify(result));
          const counts = result.data.overallCounts;
          setVisitTotal({
            totalVisits: counts.totalVisits,
            newVisits: counts.newVisits,
            inProgressVisits: counts.inProgressVisits,
            completedVisits: counts.completedVisits,
            pastDueVisits: counts.pastDueVisits ? counts.pastDueVisits : 0
          });
          let todaysVisitData = result.data.todayVisits;
          if (todaysVisitData.length > 0) {
            // setTodaysVisit({ "totalVisits": 0, "newVisits": 0, "inProgressVisits": 0, "completedVisits": 0 })
            let total = todaysVisitData[0].newVisits + todaysVisitData[0].inProgressVisits + todaysVisitData[0].completedVisits
            setTodaysVisit({ "totalVisits": total, "newVisits": todaysVisitData[0].newVisits, "inProgressVisits": todaysVisitData[0].inProgressVisits, "completedVisits": todaysVisitData[0].completedVisits })
          } else {
            setTodaysVisit({ "totalVisits": 0, "newVisits": 0, "inProgressVisits": 0, "completedVisits": 0 })
          }
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => setLoading(false));
    };
    const [visitTotal, setVisitTotal] = useState({
      totalVisits: 0,
      newVisits: 0,
      inProgressVisits: 0,
      completedVisits: 0,
      pastDueVisits: 0
    });



    useFocusEffect(
      useCallback(() => {
        loadData();
      }, [])
    );

    return (
      <>
        <View style={styles.widgetsContainer}>
          {loading && (
            <View style={styles.loaderOverlay}>
              <ActivityIndicator size="large" color="#0000ff" />
            </View>
          )}
          <TouchableOpacity style={styles.widget} onPress={onClickOpenVisit}>
            <Text allowFontScaling={false} style={[styles.widgetText]} data-testid="dashboard-new-count">{visitTotal.newVisits}</Text>
            <View id="new-visits">
              <Text allowFontScaling={false} style={styles.widgetCount}>New</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.widget, styles.widget2]}
            onPress={onClickToBeReviewed}
          >
            <Text allowFontScaling={false} style={[styles.widgetText, styles.widgetText2]} id="in-progress-visits" data-testid="dashboard-inprogress-count">
              {visitTotal.inProgressVisits}
            </Text>
            <Text allowFontScaling={false} style={styles.widgetCount}>In Progress</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.widget, styles.widget3]}
            onPress={onClickPastDueDate}
          >
            <Text allowFontScaling={false} id="past-due-visits" data-testid="dashboard-pastdue-count" style={[styles.widgetText, styles.widgetText3]}>{visitTotal.pastDueVisits}</Text>
            <Text allowFontScaling={false} style={styles.widgetCount}> Past Due</Text>
          </TouchableOpacity>
        </View>
      </>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <Animated.ScrollView
        scrollEventThrottle={16}
        style={styles.scrollContainer}
      >
        <View style={styles.visitsSection}>
          <GreetingHeader />
          {renderWidgets()}
          <UpcomingVisit />
          <VisitStatusPieChart data={todaysVisit} loading={loading} />
          {/* <BottomPopup visible={showPopup} onClose={() => hideNotification()} /> */}
          {/* <DailyReport todaysVisit={todaysVisit} /> */}
          {/* {renderNextAppointment()} */}
        </View>
      </Animated.ScrollView>

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
    backgroundColor: "#FFFFFF",
    position: "relative",
    height: 58,
    ...globalStyles.shadow,
  },
  headerTitle: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
  },
  profileBox: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 14,
  },
  scrollContainer: {
    paddingBottom: 40,
    paddingTop: 12,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  visitsSection: {
    marginTop: 8,
    flex: 1,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 16,
    color: "#1E293B",
  },
  seeAllText: {
    fontFamily: "PlusJakartaSans_600SemiBold",
    fontSize: 16,
    color: "#3B82F6",
  },
  /* Next Appointment */

  appointmentDetails: {
    flex: 1,
    gap: 4,
  },
  patientName: {
    fontFamily: "Poppins_500Medium",
    color: "#fff",
    fontSize: 16,
    marginBottom: 2,
  },
  visitType: {
    fontFamily: "PlusJakartaSans_400Regular",
    fontSize: 13,
    color: "#fff",
  },
  appointmentTime: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
    marginTop: 3,
  },
  visitTypeContainer: {
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
  },
  shortNameContainer: {
    borderRadius: 8,
    paddingVertical: 2,
    paddingHorizontal: 6,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#f26649",
  },
  shortNameText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontFamily: "Poppins_600SemiBold",
  },
  timeText: {
    fontSize: 13,
    color: "#fff",
    fontFamily: "Poppins_400Regular",
  },
  buttonsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
    marginTop: 12,
  },

  
  topRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  appointmentProfileBox: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f26649",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  border: {
    marginTop: 4,
    marginBottom: 8,
    marginHorizontal: 2,
    height: 1,
    backgroundColor: "#fff",
  },
  widgetsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 25,
    display: "flex",
    gap: 12,
    paddingHorizontal: 20,
  },
  widget: {
    flex: 1,
    backgroundColor: "#44AFC7",
    paddingVertical: 22,
    paddingHorizontal: 12,
    height: 111,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    gap: 5
  },
  widget2: {
    backgroundColor: "#F9A704",
  },
  widget3: {
    backgroundColor: "#F26649",
  },
  widgetText: {
    color: "#fff",
    fontSize: fs(28),
    fontFamily: "Poppins_700Bold",
    textAlign: 'center'
  },
  contentContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  widgetText2: {
    color: "#fff",
  },
  widgetText3: {
    color: "#fff",
  },
  widgetCount: {
    fontSize: fs(12),
    fontFamily: "Poppins_400Regular",
    color: "#fff",
    // minHeight: 42,
    textAlign: "center",
  },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 10,
    borderWidth: 2,
    borderColor: "#fff",
  },
  loaderOverlay: {
    ...StyleSheet.absoluteFillObject,
   
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10, // Ensure it appears on top
    borderRadius: 8, // Match the widget's border radius
  },
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    // backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  popupContainer: {
    backgroundColor: '#6A5AE0',
    padding: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  message: {
    color: '#ffffff',
    fontSize: 14,
    flex: 1,
    marginLeft: 8,
  },

});
