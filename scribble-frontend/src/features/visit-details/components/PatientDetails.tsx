import {
  Animated,
  Linking,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import { globalStyles } from 'src/styles';
import { screenHeight } from 'src/utils/ScreenUtils';
// import { LucideIcon, Mail, Phone, MapPin } from 'lucide-react-native';
import theme from 'src/theme';
import { AntDesign } from '@expo/vector-icons';
import { setPageTitle } from '@/src/utils/GeneralUtils';

const PatientDetails = ({ route }) => {

  const clientDetails = route.params;
  const clientIdLength = 6
  // console.log(clientDetails)
  setPageTitle('Client');
  const onClickCall = () => { };

  const onClickMail = () => { };

  const onClickMap = () => {
    const latitude = 40.74958102694104;
    const longitude = -73.98537418664596;
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;

    Linking.openURL(url);
  };

  const renderSection = (sectionTitle: string, icon: any) => (
    <View style={styles.cardHeader}>
      <View style={{ paddingTop: 5 }}>
        <AntDesign name={icon} size={16} color="#black" />
      </View>
      <Text style={styles.sectionTitle}>{sectionTitle}</Text>
    </View>
  );

  // const renderIcon = (Icon: LucideIcon, onClick: () => void) => (
  //   <TouchableOpacity style={styles.icon} onPress={onClick}>
  //     <Icon fill={'#1c73d6'} stroke={'#EFF6FF'} size={22} />
  //   </TouchableOpacity>
  // );

  const renderRow = (title: string, value: string) => (
    <View style={styles.row}>
      <Text style={styles.detailItem}>{title} </Text>
      <Text style={styles.label}> {value}</Text>
    </View>
  );

  const renderPersonalInformation = () => {
    //let clientId= clientDetails.clientStaffNo.length > clientIdLength ? clientDetails.clientStaffNo.substring(0,clientIdLength) : clientDetails.clientStaffNo
    // let clientId= clientDetails.clientId.length > clientIdLength ? clientDetails.clientId.substring(0,clientIdLength) : clientDetails.clientId
    // let clientAddress = (clientDetails.clientAddress2 && clientDetails.clientAddress2 !="" ) ? clientDetails.clientAddress1 + "," + clientDetails.clientAddress2 : clientDetails.clientAddress1;
    console.log("clientDetails",clientDetails);
    return (
      <>
        {renderSection('Personal Information', "user")}
        <View style={styles.card}>
          {renderRow('Client Id', clientDetails.clientStaffNo)}
          {/* {renderRow('Payer Source', '')} */}
          {renderRow('DOB', clientDetails.clientDob )}
          {renderRow('Age', clientDetails.clientAge)}
          {renderRow('Gender', clientDetails.clientGender)}
          {renderRow('Mobile', clientDetails.clientPrimaryPhone)}
          {renderRow('Email', clientDetails.clientEmail)}
          {renderRow('Address 1',clientDetails.clientAddress1)}
          {renderRow('Address 2',clientDetails.clientAddress2 )}
          {renderRow('City',clientDetails.clientCity )}
          {renderRow('State',clientDetails.clientState )}
          {renderRow('Zip',clientDetails.clientZip )}
          {renderRow('County',clientDetails.clientCounty )}
          {/* <View style={styles.iconsRow}>
            {renderIcon(Phone, onClickCall)}
            {renderIcon(Mail, onClickMail)}
            {renderIcon(MapPin, onClickMap)}
          </View> */}
        </View>
      </>
    );
  };

  const renderAdditionalDetails = () => {
    return (
      <>
        {renderSection('Additional Information', "exclamationcircleo")}
        <View style={styles.card}>
          {renderRow('Language', '')}
          {renderRow('Religion', '')}

        </View>
      </>
    );
  };

  return (
    <Animated.ScrollView
      scrollEventThrottle={16}
      style={styles.scrollContainer}
    >
      {renderPersonalInformation()}
      {/* {renderAdditionalDetails()} */}

      {/* {renderAddressInformation()} */}
    </Animated.ScrollView>
  );
};

const styles = StyleSheet.create({
  cardHeader: {
    flexDirection: 'row',
    gap: 6

  },
  scrollContainer: {
    display: 'flex',
    padding: 16,
    height: screenHeight - 125,
    paddingBottom: 100,
  },
  sectionTitle: {
    fontSize: 14,
    marginBottom: 10,
    fontFamily: 'Poppins_500Medium',
    color: '#251F38',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    shadowColor: 'rgba(0, 0, 0, 0.04)',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4,
    overflow: 'hidden',
    position: 'relative',
    padding: 16,
    display: 'flex',
    gap: 8,
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 4,
  },
  detailItem: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7C7887',
  },
  label: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#251F38',
    alignItems: 'flex-end',
  },
  icon: {
    width: 36,
    height: 36,
    backgroundColor: '#EFF6FF',
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  iconsRow: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    gap: 12,
    marginTop: 8,
    justifyContent: 'flex-end',
  },
});

export default PatientDetails;
