import React, { useEffect } from "react";
import { useNavigation } from "@react-navigation/native";
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Animated,
  Linking,
} from "react-native";
import MobileHeader from "src/components/mobile-header/MobileHeader";
// import { ChevronLeft, Search } from "lucide-react-native";
import { globalStyles } from "src/styles";
import FabButton from "src/components/fab-button/FabButton";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import { NavigationContainer } from "@react-navigation/native";
import { Avatar } from "react-native-paper";
// import { Calendar, Phone, User, ClipboardList ,LucideIcon,  MapPin} from "lucide-react-native";
import theme from "src/theme";
import PreviousVisits from "src/features/visit-details/components/PreviousVisits";
import { screenHeight, screenWidth } from "src/utils/ScreenUtils";
import PatientDetails from "src/features/visit-details/components/PatientDetails";
import { SafeAreaView } from "react-native-safe-area-context";
import { Feather } from '@expo/vector-icons';

const Tab = createMaterialTopTabNavigator();

const VisitDetailsScreen = ({ route }) => {
  
  const navigation = useNavigation();

  const { visit } = route.params;
  // console.log("--Visit Details--- "+JSON.stringify(visit))
  const clientId= visit.clientId
  // console.log("clientId: "+clientId)
  // console.log(visit)
  const onGoBack = () => {
    navigation.goBack();
  };
  const onClickCall = () => {};

  const onClickMail = () => {};

  const onClickMap = () => {
    const latitude = 40.74958102694104;
    const longitude = -73.98537418664596;
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;

    Linking.openURL(url);
  };
  const navigateToRecordScreen = () => {
    navigation.navigate("Recorder", { visit });
  };
  // const renderIcon = (Icon: LucideIcon, onClick: () => void) => (
  //   <TouchableOpacity style={styles.icon} onPress={onClick}>
  //     <Icon fill={'#1c73d6'} stroke={'#EFF6FF'} size={16} />
  //   </TouchableOpacity>
  // );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onGoBack}>
          {/* <ChevronLeft size={30} color={theme.colors.text} /> */}
          <Feather
            name="chevron-left"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <View style={styles.profileBox}>
          <Text style={styles.profileInitials}>
            {visit?.clientFirstName && visit.clientFirstName[0] || "P"} 
          </Text>
        </View>
        <View style={{ flex: 1 }}>
          <Text style={styles.patientName}>
            {visit.clientFirstName} {visit.clientLastName}
          </Text>
          {visit.serviceCode != undefined || null ?
             <Text style={styles.visitType}>{visit.serviceCode}</Text>
             : null
          }
        </View>
        <View style={styles.iconsRow}>
            {/* {renderIcon(Phone, onClickCall)} */}
            {/* {renderIcon(Mail, onClickMail)} */}
            {/* {renderIcon(MapPin, onClickMap)} */}
          </View>
      </View>
      <View style={styles.tabContainer}>
        <Tab.Navigator
          screenOptions={({ route }) => ({
            tabBarStyle: styles.tabBar,
            tabBarLabelStyle: styles.tabLabel,
            tabBarLabel: ({ focused }) =>
              focused ? (
                <Text style={[styles.tabLabel, styles.tabLabelSelected]}>
                  {route.name}
                </Text>
              ) : (
                <Text style={styles.tabLabel}>{route.name}</Text>
              ),
            tabBarIndicatorStyle: styles.tabIndicator,
            lazy: true,
          })}
        >
          <Tab.Screen name="Client Details" initialParams={visit} component={PatientDetails} />
          <Tab.Screen name="Visits" initialParams={{"clientId":clientId}} component={PreviousVisits} />
        </Tab.Navigator>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    // borderBottomWidth: 1,
    // borderBottomColor: "#E2E8F0",
    backgroundColor: "#FFFFFF",
    position: "relative",
    height: 65,
    // ...globalStyles.shadow,
  },
  backButton: {
    // alignItems: "center",
    // justifyContent: "center",
    // marginRight: 12,
  },
  profileBox: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#CBD5E1",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 16,
  },
  patientName: {
    fontFamily: "Poppins_500Medium",
    color: "#251F38",
    fontSize: 16,
    marginBottom: 2,
  },
  visitType: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    color: "rgba(37, 31, 56, 0.70)",
  },
  tabBar: {
    backgroundColor: "#fff",
  },
  tabLabel: {
    color: '#7C7887',
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
  },
  tabLabelSelected: {
    color: "#1D73D6",
  },
  tabIndicator: {
    backgroundColor: "#1D73D6",
    height: 3,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  sectionContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  detailItem: {
    fontSize: 16,
    marginBottom: 5,
  },
  label: {
    fontWeight: "bold",
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F3F4F6",
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
  cardSubtitle: {
    fontSize: 14,
    color: "#666",
  },
  status: {
    color: "#4F46E5",
    fontWeight: "bold",
    marginTop: 5,
  },
  tabContainer: {
    display: "flex",
    flex: 1,
    height: screenHeight - 65,
  },
  icon: {
    width: 36,
    height: 36,
    backgroundColor: '#EFF6FF',
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  iconsRow: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    gap: 12,
    marginTop: 8,
    justifyContent: 'flex-end',
  },
});

export default VisitDetailsScreen;
