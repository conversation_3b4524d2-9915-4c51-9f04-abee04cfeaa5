import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { Audio } from "expo-av";

export type PickedAudio = {
  uri: string;           // ← use this in your upload code
  name?: string;
  sizeBytes?: number;
  durationMs?: number;
  mimeType?: string;
};

export async function pickAudio(): Promise<PickedAudio | null> {
  const res = await DocumentPicker.getDocumentAsync({
    type: ["audio/*"],
    copyToCacheDirectory: true,
    multiple: false,
  });
  if (res.canceled) return null;

  const asset = res.assets?.[0];
  if (!asset?.uri) return null;

  // size (fallback to FS)
  let sizeBytes = asset.size;
  if (sizeBytes == null) {
    try {
      const stat = await FileSystem.getInfoAsync(asset.uri);
      if (stat.exists && typeof stat.size === "number") sizeBytes = stat.size;
    } catch {}
  }

  // duration via expo-av
  let durationMs: number | undefined;
  const sound = new Audio.Sound();
  try {
    await sound.loadAsync({ uri: asset.uri }, { shouldPlay: false });
    const status = await sound.getStatusAsync();
    if ("durationMillis" in status && typeof status.durationMillis === "number") {
      durationMs = status.durationMillis;
    }
  } catch {}
  finally {
    try { await sound.unloadAsync(); } catch {}
  }

  return {
    uri: asset.uri,
    name: asset.name,
    sizeBytes,
    durationMs,
    mimeType: asset.mimeType,
  };
}

