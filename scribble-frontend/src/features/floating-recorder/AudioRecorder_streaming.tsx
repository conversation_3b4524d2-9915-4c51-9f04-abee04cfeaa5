import React, { useState, useRef, useEffect, useCallback, use } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
  Dimensions,
  ActivityIndicator,
  Image,
  Modal,
  StatusBar,
  ScrollView,
  BackHandler,
  Platform,
  Pressable,
  NativeModules,
  NativeEventEmitter,
  Linking,
  PermissionsAndroid,
  AppState
} from 'react-native';
import { Audio } from 'expo-av';
// import {
//   ChevronDown,
//   ChevronUp,
//   X,
// } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from 'src/redux/store';

import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';

const MINI_PLAYER_HEIGHT = 55;
const EXPANDED_HEIGHT = screenHeight;
import { FontAwesome, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');
import { uploadRecord, fetchAssesmentId, fetchFormDetails, uploadChunk, uploadFinalChunk } from './record-api';
import ToastService from '@/src/utils/toastService';
import { hideRecorder, resetRecorder, toggleExpanded } from '@/src/redux/slices/audioRecorderSlice';
import { DynamicMessageModal, DynamicMessageModalWithConfirm, MessageModal } from '@/src/utils/MessageModal';
import theme from '@/src/theme';
import { GlobalFunctions } from '@/src/utils/GlobalFunction';
import { Feather } from '@expo/vector-icons';
import { setSecureItem, getSecureItem, removeSecureItem } from '@/src/utils/cryptoHelper';
import { ENABLE_MICROPHONE, ENABLE_MICROPHONE_TITLE, NO_SPACE_ERROR, NO_SPACE_ERROR_TITLE, UPLOAD_MINI_ERROR, UPLOAD_MINI_ERROR_WITH_INTERNET } from '@/src/components/messages';
import { get } from 'react-native/Libraries/TurboModule/TurboModuleRegistry';
import NetInfo from '@react-native-community/netinfo';
const { RecorderModule } = NativeModules;
import { Paths } from 'expo-file-system';
import * as Sentry from '@sentry/react-native';
import { TextInput } from 'react-native-paper';
import AudioPickButton from './AudioPickButton';

const FOOTER_HEIGHT = 110;
const HANDLE_HEIGHT = 5;
const MINIMUM_SPACE_FOR_RECORDING = 50 * 1024 * 1024; // 50 MB
//const MINIMUM_SPACE_FOR_RECORDING = 29670009344

const MicrophonePermissionModal = ({ visible, onCancel, onSettings }) => {
  const openSettings = async () => {
    try {
      if (Platform.OS === 'ios') {
        // For iOS, open app-specific settings
        await Linking.openURL('app-settings:');
      } else if (Platform.OS === 'android') {
        // For Android, open app-specific settings
        await Linking.openSettings();
      } else {

      }
      onSettings?.();
    } catch (error) {
      console.error('Error opening settings:', error);
      Alert.alert('Error', 'Unable to open settings. Please go to your device settings manually.');
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <MaterialIcons name="multitrack-audio" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={styles.title}>{ENABLE_MICROPHONE_TITLE}</Text>
          <Text style={styles.message}>
            {ENABLE_MICROPHONE}
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={openSettings}>
              <Text style={styles.confirmButtonText}>Settings</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};



const FullScreenLoader = ({ visible, onCancel, onConfirm, value }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <Ionicons name="checkmark-circle" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={styles.title}>Submit Recording?</Text>
          <Text style={styles.message}>
            You have {value} unanswered questions. You may complete them before submitting to AI?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
const RecordDeleteModal = ({ visible, onCancel, onConfirm }) => {
  const [deleteStr, setDeleteStr] = useState("")
  const onDeletePress = useCallback(() => {
    if (deleteStr === "delete") {
      console.log("Deleting recording...");
      onConfirm()
    }
  }, [])
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <Ionicons name="checkmark-circle" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={styles.title}>Delete Recording?</Text>
          <Text style={styles.message}>
            If you have an ongoing recording, it will be deleted. Do you want to continue?
          </Text>
          {/* <Text style={{ marginTop: 10, fontSize: 12, color: 'gray' }}>Type "delete" to confirm</Text>
          <View style={{ borderWidth: 1, borderColor: 'gray', borderRadius: 5, marginTop: 10, width: '80%', alignSelf: 'center' }}>
            <TextInput
              style={{ height: 40, paddingHorizontal: 10 }}
              placeholder="Type here"
              value={deleteStr}
              onChangeText={setDeleteStr}
              autoCorrect={false} />
          </View> */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const RestartRecordModal = ({ visible, onCancel, onConfirm }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <Image style={{
                height: 50, width: 50
              }} source={require('assets/images/restart-2.png')} />
            </View>
          </View>
          <Text style={styles.title}>Restart Recording?</Text>
          <Text style={styles.message}>
            Your recording will be deleted,
            is that okay?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Restart</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const AudioRecorder = ({
  isRecording,
  recordingURI,
  setIsRecording,
  setRecordingURI,
  assesId,
  navigation,
  showPauseResume,
  setShowPauseResume,
  initialExpanded = false
}) => {
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  //console.log("AudioRecorder Rendered :",showPauseResume )
  // const { requestWakeLock, releaseWakeLock, isSupported: wakeLockSupported, isActive: wakeLockActive } = useWakeLock();
  // console.log("assesId:", assesId)
  // Animation values
  const translateYAnim = useRef(new Animated.Value(initialExpanded ? 0 : screenHeight)).current;
  const backdropOpacity = useRef(new Animated.Value(initialExpanded ? 0.5 : 0)).current;
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Track if we're in the middle of a gesture
  const isDragging = useRef(false);
  const startY = useRef(0);
  const currentY = useRef(0);

  const currentRoute = useSelector(
    (state: RootState) => state.route.currentRoute,
  );

  const [checkedCount, setCheckedCount] = useState(0);

  const recordingRef = useRef(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isFirstTimeRecording, setIsFirstTimeRecording] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [restartLoading, setRestartLoading] = useState(false);
  const [cancelRecording, setCancelRecording] = useState(false);
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const [playbackPosition, setPlaybackPosition] = useState(0);
  // const [showPauseResume, setShowPauseResume] = useState(false);
  const [assesmentId, setAssesmentId] = useState(0);

  const [showMessageModal, setShowMessageModal] = useState(false)
  // const [message, setMessage] = useState("")
  // const [messageType, setMessageType] = useState("success")
  const [msgTitle, setMsgTitle] = useState("")
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showMicrophoneModal, setShowMicrophoneModal] = useState(false);
  const recordingTimeRef = useRef(recordingTime);


  const [uploadError, setUploadError] = useState(null);
  const [recordingFileName, setRecordingFileName] = useState(null);
  const [failedRecordingUri, setFailedRecordingUri] = useState(null);
  const [isOffline, setIsOffline] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingMessage, setProcessingMessage] = useState("");

  const [chunkUploadQueue, setChunkUploadQueue] = useState([]);
  const [uploadingChunks, setUploadingChunks] = useState(new Set());
  const [isProcessingChunks, setIsProcessingChunks] = useState(false);


  const [categories, setCategories] = useState([{
    container: { id: '', heading: '', subHeading: '' },
    items: []
  }]);
  let [totalQuestions, setTotalQuestions] = useState(0);
  let [progress, setProgress] = useState(0);
  const recorderEventEmitter = new NativeEventEmitter(RecorderModule);

  useEffect(() => {
    const emitter = new NativeEventEmitter(RecorderModule);

    const subReady = emitter.addListener("RecordingSegmentReady", async ({ path, index, duration }) => {
      console.log(`\n\n 🎙️ seg ${index} is created and the path is: ${path} (index: ${index}) duration is --> ${duration}`);
      try {
        // Extract segment name from path
        const pathParts = path.split('/');
        const segmentName = pathParts[pathParts.length - 1];
        const segmentIndex = extractIndexFromSegmentName(segmentName);

        // Save as pending
        await saveChunkStatus({
          index: segmentIndex,
          path: path,
          name: segmentName,
          size: 0 // Size will be available from getStoredSegments
        }, 'pending');

        const chunkData = {
          path,
          index: segmentIndex,
          segmentName,
          assessmentId: assesmentId,
          duration,
          retryCount: 0
        };

        const netInfo = await NetInfo.fetch();

        if (!netInfo.isConnected) {
          console.log('No internet - adding chunk directly to failed queue');
          await addToFailedChunks(chunkData);
          // Sentry.addBreadcrumb({
          //   category: 'No Internet',
          //   message: 'No Internet - chunk added to failed queue',
          //   level: 'info',
          //   data: {
          //     assesmentId: assesmentId ?? null,
          //     message: `seg ${index} is created and the path is: ${path} (index: ${index}) duration is --> ${duration}`
          //   },
          // });

          // Sentry.captureMessage('No Internet - chunk added to failed queue', {
          //   level: 'info',
          //   extra: {
          //     assesmentId: assesmentId ?? null,
          //     message: `seg ${index} is created and the path is: ${path} (index: ${index}) duration is --> ${duration}`
          //   },
          //   tags: {
          //     feature: 'audio-recorder-internet',
          //     platform: Platform.OS,


          //   },
          // });
        } else {
          console.log('Internet available - adding to upload queue');

          // Sentry.addBreadcrumb({
          //   category: ' Internet Available',
          //   message: 'Internet Available',
          //   level: 'info',
          //   data: {
          //     assesmentId: assesmentId ?? null,
          //     message: `seg ${index} is created and the path is: ${path} (index: ${index}) duration is --> ${duration}`
          //   },
          // });

          // Sentry.captureMessage('Internet Available', {
          //   level: 'info',
          //   extra: {
          //     assesmentId: assesmentId ?? null,
          //     message: `seg ${index} is created and the path is: ${path} (index: ${index}) duration is --> ${duration}`
          //   },
          //   tags: {
          //     feature: 'audio-recorder-internet',
          //     platform: Platform.OS,


          //   },
          // });
          setChunkUploadQueue(prev => [...prev, chunkData]);
          setTimeout(() => processChunkUploadQueue(), 1000);
        }
      } catch (error) {
        console.error('Error handling segment ready:', error);
      }
    });


    return () => {
      subReady.remove();
      // subCreated.remove();
    };
  }, [assesmentId]);
  useEffect(() => {
    let intervalId;

    // Only set up periodic processing if we have an assessment ID
    //console.log("Schedular Loading : ",assesmentId, isProcessingChunks)
    if (assesmentId) {
      intervalId = setInterval(async () => {
        try {
          const netInfo = await NetInfo.fetch();

          if (netInfo.isConnected && !isProcessingChunks) {
            const failedChunks = await getFailedChunks();
            const pendingChunks = await getPendingChunks();

            if (failedChunks.length > 0 || pendingChunks.length > 0) {
              console.log(`Periodic check: Found ${failedChunks.length} failed and ${pendingChunks.length} pending chunks`);
              processChunkUploadQueue();
            }
          }
        } catch (error) {
          console.error('Error in periodic chunk check:', error);
        }
      }, 30000); // Check every 30 seconds
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [assesmentId, isProcessingChunks]);

  useEffect(() => {
    const restoreRecordingState = async () => {
      const storedRecording = await getSecureItem("recording");
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);

        // Check if upload failed
        if (recordingData.uploadFailed) {
          setUploadError("Error");
          setRecordingURI(recordingData.recording_uri);
          return;
        }

        // Restore recording session
        //console.log(" Restoring recording state status : ", recordingData.isRecording, recordingData.isPaused);
        if (recordingData.isRecording || recordingData.isPaused) {

          console.log("......Restoring recording state...");
          setRecordingURI(recordingData.recording_uri);


          setIsPaused(true);
          setIsRecording(false);
          setShowPauseResume(true);

          //console.log("......Restoring recording state... : true");
          setIsFirstTimeRecording(false);
          setCancelRecording(true);

          await setSecureItem("recording", JSON.stringify({
            ...recordingData,
            "isRecording": false,
            "isPaused": true,
            //"recordingTime": recordingData.recordingTime || 0
          }));


        }
        try {
          if ((Platform.OS === 'ios' || Platform.OS === 'android') && (recordingData.isRecording || recordingData.isPaused)) {
            console.log("Attempting to recreate recorder with URI 2:", recordingData.recording_uri);

            const recordings = await loadStoredRecordings("reload");
            let lastpath: String = ""
            if (recordings.length > 0) {
              lastpath = recordings[recordings.length - 1].path;
              console.log("Restored recording segments-->:", JSON.stringify(lastpath));
            }
            // const newSegUri = await RecorderModule.recreateRecorder(recordingData.recording_uri);
            const newSegUri = await RecorderModule.recreateRecorder(lastpath);
            console.log("Restored recording new segments:", JSON.stringify(newSegUri));

            recordingRef.current = newSegUri; // important
            const storedData = await getSecureItem("recording");
            if (storedData) {
              const recordingData = JSON.parse(storedData);

              await setSecureItem("recording", JSON.stringify({
                ...recordingData,
                recording_uri: newSegUri,


              }));
            }
            //if (assesmentId) {
            setTimeout(() => processChunkUploadQueue(), 2000);
            //}
          }
        } catch (e) {
          console.warn('Failed to recreate recorder after relaunch:', e);
        }

      }
    };

    restoreRecordingState();
    (async () => {
      const stored = await getSecureItem("recording");
      if (stored) {
        if (stored) {
          const data = JSON.parse(stored);
          //setRecordedSegments(data.segments || []);
        }
      }
    })();
  }, [assesmentId]);


  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const wasOffline = isOffline;
      setIsOffline(!state.isConnected);

      if (wasOffline && state.isConnected && assesmentId) {
        console.log('Network restored - processing failed chunks');
        setTimeout(() => processChunkUploadQueue(), 2000);
      }
    });

    return () => unsubscribe();
  }, [isOffline, assesmentId]);

  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      if (nextAppState === 'background' && (isRecording || isPaused)) {
        // Save current state when going to background

        console.log("App going to background, saving state...");
        const storedRecording = await getSecureItem("recording");
        if (storedRecording) {
          const recordingData = JSON.parse(storedRecording);
          await setSecureItem("recording", JSON.stringify({
            ...recordingData,
            "recordingTime": recordingTime,
            "isRecording": isRecording,
            "isPaused": isPaused
          }));
        }

      }
      if (nextAppState === 'active') {
        console.log("App has come to the foreground!");
        if (showPauseResume && isPaused) {
          ToastService.show({
            message: "Your Recording is paused now.",
            type: "info",
            duration: 4000,
            position: "top",
            bottomOffset: 80,
            autoHide: true,
            backgroundColor: theme.colors.buttonColor,
            icon: "checkmark-circle"
          });
        }

      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [isRecording, isPaused, recordingTime]);

  const fetchAssesment = () => {
    fetchFormDetails(assesId)
      .then((result) => {
        if (result.status == "ok") {
          let assesQues = result.data.question;
          let totalCount = 0

          for (let i = 0; i < assesQues.length; i++) {
            for (let j = 0; j < assesQues[i].items.length; j++) {
              if (!assesQues[i].items[j].isContainer && assesQues[i].items[j].isDisplayCheatSheet) {
                assesQues[i].items[j].checked = false;
                totalCount++
              }
            }
          }
          setTotalQuestions(totalCount);
          setCategories(assesQues);
        } else {
          Alert.alert(result.errorMessage);
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => setLoading(false));
  };

  const extractIndexFromSegmentName = (segmentName) => {
    // Extract index from names like "seg_0001.m4a"
    const match = segmentName.match(/seg_(\d+)\.m4a/);
    return match ? parseInt(match[1], 10) : 0;
  };

  const updateRecordingWithChunkData = async (updateFn) => {
    try {
      const storedRecording = await getSecureItem("recording");
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);

        // Initialize arrays if not exists
        if (!recordingData.chunks) {
          recordingData.chunks = [];
        }
        if (!recordingData.failedChunks) {
          recordingData.failedChunks = [];
        }

        const updatedData = updateFn(recordingData);
        await setSecureItem("recording", JSON.stringify(updatedData));
        return updatedData;
      }
      return null;
    } catch (error) {
      console.error('Error updating recording with chunk data:', error);
      return null;
    }
  };

  // 4. Chunk management functions
  const saveChunkStatus = async (segmentData, status) => {
    const { index, path, name, size } = segmentData;

    await updateRecordingWithChunkData((data) => {
      //console.log("Missed data :", JSON.stringify(data));
      const existingIndex = data.chunks.findIndex(chunk => chunk.index === index);

      const chunkData = {
        index,
        status, // 'pending', 'uploading', 'uploaded', 'failed'
        path,
        segmentName: name,
        size,
        timestamp: Date.now(),
        retryCount: existingIndex >= 0 ? (data.chunks[existingIndex].retryCount || 0) : 0
      };
      console.log("Saving chunk status:", JSON.stringify(chunkData));
      if (existingIndex >= 0) {
        data.chunks[existingIndex] = { ...data.chunks[existingIndex], ...chunkData };
      } else {
        data.chunks.push(chunkData);
      }

      return data;
    });
  };

  const markChunkAsUploaded = async (chunkIndex) => {
    await updateRecordingWithChunkData((data) => {
      const chunkArrayIndex = data.chunks.findIndex(chunk => chunk.index === chunkIndex);
      if (chunkArrayIndex >= 0) {
        data.chunks[chunkArrayIndex].status = 'uploaded';
        data.chunks[chunkArrayIndex].timestamp = Date.now();
      }

      // Remove from failed chunks
      data.failedChunks = data.failedChunks.filter(chunk => chunk.index !== chunkIndex);

      return data;
    });
  };

  const addToFailedChunks = async (chunkData) => {
    await updateRecordingWithChunkData((data) => {
      const existingFailedIndex = data.failedChunks.findIndex(chunk => chunk.index === chunkData.index);
      const failedChunk = {
        ...chunkData,
        timestamp: Date.now(),
        error: chunkData.error || 'Upload failed'
      };

      if (existingFailedIndex >= 0) {
        data.failedChunks[existingFailedIndex] = failedChunk;
      } else {
        data.failedChunks.push(failedChunk);
      }

      // Update status in chunks array
      const chunkArrayIndex = data.chunks.findIndex(chunk => chunk.index === chunkData.index);
      if (chunkArrayIndex >= 0) {
        data.chunks[chunkArrayIndex].status = 'failed';
        data.chunks[chunkArrayIndex].retryCount = (data.chunks[chunkArrayIndex].retryCount || 0) + 1;
      }

      return data;
    });
  };

  const getRecordingData = async () => {
    try {
      const storedRecording = await getSecureItem("recording");
      return storedRecording ? JSON.parse(storedRecording) : null;
    } catch (error) {
      console.error('Error getting recording data:', error);
      return null;
    }
  };

  const getPendingChunks = async () => {
    const data = await getRecordingData();
    if (!data?.chunks) return [];

    return data.chunks.filter(chunk =>
      chunk.status === 'pending' || chunk.status === 'failed'
    );
  };
  const getUploadedChunks = async () => {
    const data = await getRecordingData();
    console.log("Get uploaded chunks :", JSON.stringify(data));
    if (!data?.chunks) return [];

    return data.chunks.filter(chunk =>
      chunk.status === 'uploaded'
    );
  };

  const getFailedChunks = async () => {
    const data = await getRecordingData();
    return data?.failedChunks || [];
  };

  const uploadChunkWithRetry = async (chunkData, maxRetries = 3) => {
    console.log("Uploading chunk with retry:", JSON.stringify(chunkData),assesmentId);
    const { path, index, segmentName, retryCount = 0 } = chunkData;

    try {
      console.log(`Uploading chunk ${index} (${segmentName}), attempt ${retryCount + 1}`);

      // Mark as uploading
      await saveChunkStatus({
        index,
        path,
        name: segmentName,
        size: chunkData.size || 0
      }, 'uploading');

      setUploadingChunks(prev => new Set(prev).add(`${assesmentId}_${index}`));

      const result = await uploadChunk(path, assesmentId, index, segmentName);

      if (result.status === "ok") {
        await markChunkAsUploaded(index);

        setUploadingChunks(prev => {
          const newSet = new Set(prev);
          newSet.delete(`${assesmentId}_${index}`);
          return newSet;
        });

        console.log(`Chunk ${index} (${segmentName}) uploaded successfully`);
        return true;
      } else {
        throw new Error(result.errorMessage || 'Upload failed');
      }
    } catch (error) {
      console.error(`Chunk ${index} (${segmentName}) upload failed:`, error);

      setUploadingChunks(prev => {
        const newSet = new Set(prev);
        newSet.delete(`${assesmentId}_${index}`);
        return newSet;
      });

      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Scheduling retry for chunk ${index} in ${delay}ms...`);

        setTimeout(async () => {
          // 🔥 NEW: Check network connectivity before retrying
          try {
            const netInfo = await NetInfo.fetch();

            if (!netInfo.isConnected) {
              console.log(`Retry cancelled for chunk ${index} - no internet connection`);

              // Add to failed chunks instead of retrying
              await addToFailedChunks({
                ...chunkData,
                error: 'No internet connection',
                retryCount: retryCount + 1
              });

              return false;
            }

            // Network available, proceed with retry
            console.log(`Retrying chunk ${index} (attempt ${retryCount + 2})`);
            const retryData = { ...chunkData, retryCount: retryCount + 1 };
            uploadChunkWithRetry(retryData, maxRetries);

          } catch (netError) {
            console.error('Error checking network for retry:', netError);

            // Assume offline on error, add to failed chunks
            await addToFailedChunks({
              ...chunkData,
              error: 'Network check failed',
              retryCount: retryCount + 1
            });
          }
        }, delay);

      } else {
        await addToFailedChunks({ ...chunkData, error: error.message });
        console.error(`Chunk ${index} failed after ${maxRetries} retries`);
      }

      return false;
    }
  };
  const processChunkUploadQueue = async () => {
    console.log('Processing chunk upload queue...', isProcessingChunks, isOffline,assesmentId);
    if (isProcessingChunks || isOffline) {
      console.log('Chunk processing skipped:', { isProcessingChunks, isOffline });
      return;
    }

    setIsProcessingChunks(true);

    try {
      // Process failed chunks first
      const failedChunks = await getFailedChunks();
      for (const chunk of failedChunks) {
        const uploadKey = `${chunk.assessmentId || assesmentId}_${chunk.index}`;
        if (!uploadingChunks.has(uploadKey)) {
          
          if(assesmentId && assesmentId !=0){
            console.log('Retrying failed chunk:', chunk.index);
          uploadChunkWithRetry(chunk);
          await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      // Process pending chunks
      const pendingChunks = await getPendingChunks();

      for (const chunk of pendingChunks) {
        const uploadKey = `${assesmentId}_${chunk.index}`;
        if (!uploadingChunks.has(uploadKey)) {
          
          if(assesmentId && assesmentId !=0){
            console.log('Processing pending chunk:', chunk.index, assesmentId);
          uploadChunkWithRetry({ ...chunk, assessmentId: assesmentId });
          await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      // Process new chunks from queue
      const chunksToProcess = [...chunkUploadQueue];
      setChunkUploadQueue([]);

      for (const chunk of chunksToProcess) {
        const uploadKey = `${chunk.assessmentId}_${chunk.index}`;
        if (!uploadingChunks.has(uploadKey)) {
          if(assesmentId && assesmentId !=0){
          console.log('Processing queued chunk:', chunk.index);
          uploadChunkWithRetry(chunk);
          await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }
    } catch (error) {
      console.error('Error processing chunk upload queue:', error);
    } finally {
      setIsProcessingChunks(false);
    }
  };


  useEffect(() => {
    const eventEmitter = new NativeEventEmitter(RecorderModule);
    const subscription = eventEmitter.addListener("RecordingInterruption", (event) => {
      console.log("Native interruption event:", event);
      if (event.status === 'paused') {
        console.log("paused")

        setIsRecording(false)
        setIsPaused(true);
        ToastService.show({
          message: "Recording paused - mic in use by another app",
          type: "info",
          duration: 4000,
          position: "bottom",
          bottomOffset: 80,
          autoHide: true,
          backgroundColor: theme.colors.buttonColor,
          icon: "checkmark-circle"
        });
        //RecorderModule.pauseRecording();

      } else if (event.status === 'resumed') {
        //setInterruptedPause(false);
        // resumeTimer(); // Resume the timer after call ends
        console.log("resumed")
        // _resumeRecording()
        setIsRecording(true)
        setIsPaused(false);
      }
    });

    return () => subscription.remove();
  }, []);

  useEffect(() => {
    const eventEmitter = new NativeEventEmitter(RecorderModule);
    const subscription = eventEmitter.addListener("MemoryLowInterruption", (event) => {
      console.log("Native interruption event:", event);
      if (event.status === 'paused') {
        console.log("paused")

        setIsRecording(false)
        setIsPaused(true);
        ToastService.show({
          message: "Recording paused..Insufficient storage space.",
          type: "info",
          duration: 4000,
          position: "bottom",
          bottomOffset: 80,
          autoHide: true,
          backgroundColor: theme.colors.buttonColor,
          icon: "checkmark-circle"
        });
        //RecorderModule.pauseRecording();

      }
    });

    return () => subscription.remove();
  }, []);

  useEffect(() => {
    setAssesmentId(assesId)
    fetchAssesment();
  }, [assesId]);

  // Animated transitions for the bottom sheet
  useEffect(() => {
    if (isExpanded) {
      // Animate bottom sheet up from bottom
      Animated.parallel([
        Animated.spring(translateYAnim, {
          toValue: 0,
          useNativeDriver: true,
          friction: 8,
          tension: 65,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      // Prevent body scrolling when sheet is open (web only)
      if (Platform.OS === 'web') {
        document.body.style.overflow = 'hidden';
      }
    } else {
      // Animate bottom sheet down
      Animated.parallel([
        Animated.spring(translateYAnim, {
          toValue: screenHeight,
          useNativeDriver: true,
          friction: 8,
          tension: 65,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => {
        // After animation completes, we could do cleanup if needed
      });

      // Restore body scrolling when sheet is closed (web only)
      if (Platform.OS === 'web') {
        document.body.style.overflow = '';
      }
    }

    // Cleanup when component unmounts
    return () => {
      if (Platform.OS === 'web') {
        document.body.style.overflow = '';
      }
    };
  }, [isExpanded]);
  useEffect(() => {
    recordingTimeRef.current = recordingTime;
  }, [recordingTime]);
  const closeRecordingLogout = useCallback(async () => {
    if (!uploadError) {
      const storedRecording = await getSecureItem("recording");
      //console.log("storedRecording-->", storedRecording, recordingTimeRef.current)
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);
        await setSecureItem("recording", JSON.stringify({
          ...recordingData,
          "recordingTime": recordingTimeRef.current,
          "isRecording": false,
          "isPaused": true
        }));
      }
    }
    if (isRecording) {
      await _pauseRecording()
    }
    recordingRef.current = null;
    setIsRecording(false);
    setIsPaused(false);
    // let success = await _stopRecording()
    //  if (success) {
    setRecordingURI(null)
    dispatch(hideRecorder());
    // }
    //dispatch(hideRecorder());
  }, [isRecording, isPaused, uploadError]);

  useEffect(() => {
    // Register the closeRecording function globally
    GlobalFunctions.closeRecording = closeRecordingLogout;

    // Clean up when the component unmounts
    return () => {
      GlobalFunctions.closeRecording = null;

    };
  }, []);

  // Format the time as MM:SS
  const formatTime = seconds => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Increment the timer
  useEffect(() => {
    let interval;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);


  // Clean up audio when component unmounts
  useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);


  const ensureMicPermission = async () => {
    if (Platform.OS !== 'android') return true;
    const status = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO
    );
    return status === PermissionsAndroid.RESULTS.GRANTED;
  };

  const _startRecording = async () => {
    try {
      const freeBytes = Paths.availableDiskSpace;
      console.log("Available disk space (bytes):", freeBytes);
      console.log(`Free storage: ${(freeBytes / (1024 * 1024)).toFixed(2)} MB`);

      if (freeBytes < MINIMUM_SPACE_FOR_RECORDING) {
        setShowMessageModal(true);
        return
      }
      console.log('Requesting permissions...');
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        setShowMicrophoneModal(true); // Show the new microphone modal
        return false;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      if (Platform.OS === 'web') {
        try {
          const recording = new Audio.Recording();
          const recordingOptions = {
            ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
            web: {
              mimeType: 'audio/mp4',
              bitsPerSecond: 128000,
            },
          };

          await recording.prepareToRecordAsync(recordingOptions);
          await recording.startAsync();
          recordingRef.current = recording;
          setIsRecording(true);
          setIsPaused(false);
          setRecordingTime(0);
          console.log('Recording started web: true');
          setShowPauseResume(true);
          return true;
        } catch (safariError) {
          console.error('Safari recording attempt failed:', safariError);
          Alert.alert(
            'Browser Limitation',
            'Audio recording is not fully supported in Safari. Please try Chrome or Firefox for the best experience.',
            [{ text: 'OK' }]
          );
          return false;
        }
      }
      else if (Platform.OS === 'android') {
        const ok = await ensureMicPermission();
        if (!ok) { setShowMicrophoneModal(true); return false; }

        const uri = await RecorderModule.startRecording();
        recordingRef.current = uri;

        setIsRecording(true);
        setIsPaused(false);
        setRecordingTime(0);
        console.log('Recording started');
        setShowPauseResume(true);
        await setSecureItem("recording", JSON.stringify({
          "assesmentId": assesmentId,
          "recording_uri": uri,
          "email": await getSecureItem("email"),
          "isRecording": true,
          "isPaused": false,
          "recordingTime": 0,
          "startTime": Date.now(),
          "chunks": [],
          "failedChunks": []
        }));
        return true;
      }
      else {
        const permission = await Audio.requestPermissionsAsync();
        if (!permission.granted) {
          setShowMicrophoneModal(true);
          return false;
        }
        console.log('Starting recording on iOS...');
        const uri = await RecorderModule.startRecording();
        console.log('Recording started with URI:', uri);
        recordingRef.current = uri;

        setIsRecording(true);
        setIsPaused(false);
        setRecordingTime(0);
        console.log('Recording started ios :true');
        setShowPauseResume(true);
        await setSecureItem("recording", JSON.stringify({
          "assesmentId": assesmentId,
          "recording_uri": uri,
          "email": await getSecureItem("email"),
          "isRecording": true,
          "isPaused": false,
          "recordingTime": 0,
          "startTime": Date.now(),
          "chunks": [],
          "failedChunks": []
        }));
        return true;
      }

    } catch (error) {
      console.error('Error starting recording:', error);
      return false;
    }
  };

  const onMicrophoneModalCancel = () => {
    setShowMicrophoneModal(false);
  };

  const onMicrophoneModalSettings = () => {
    setShowMicrophoneModal(false);
  };

  const _pauseRecording = async () => {
    console.log('Pausing recording...');
    ToastService.show({
      message: "You recording has been paused.",
      type: "info",
      duration: 4000,
      position: "top",
      bottomOffset: 80,
      autoHide: true,
      backgroundColor: theme.colors.buttonColor,
      icon: "checkmark-circle"
    });

    if (!recordingRef.current) return;

    try {
      if (Platform.OS === "ios" || Platform.OS === "android") {
        // Just pause - no expensive finalization
        await RecorderModule.pauseRecording();

        // Update stored state without finalization
        const storedRecording = await getSecureItem("recording");
        if (storedRecording) {
          const recordingData = JSON.parse(storedRecording);
          await setSecureItem("recording", JSON.stringify({
            ...recordingData,
            "isRecording": false,
            "isPaused": true,
            "recordingTime": recordingTime,
            "pausedAt": Date.now(),
          }));

          // Add breadcrumb without finalization
          Sentry.addBreadcrumb({
            category: 'Recording is Paused',
            message: 'Recording is Paused',
            level: 'info',
            data: {
              assesmentId: recordingData?.assesmentId ?? null,
              RecordingUri: recordingData.recording_uri, // Keep original URI
              screenName: "Recording Screen"
            },
          });

          Sentry.captureMessage('Recording is Paused', {
            level: 'info',
            extra: {
              assesmentId: recordingData?.assesmentId ?? null,
              RecordingUri: recordingData.recording_uri,
              screenName: "Recording Screen"
            },
            tags: {
              feature: 'audio-recorder',
              platform: Platform.OS,
              auto_resume_upload: 'true',
            },
          });
        }
      } else {
        await recordingRef.current.pauseAsync();
      }

      setIsRecording(false);
      setIsPaused(true);
      setCancelRecording(true);

    } catch (err) {
      console.error('Failed to pause recording', err);
    }
  };

  const _resumeRecording = async () => {
    try {
      const isNative = Platform.OS === 'ios' || Platform.OS === 'android';

      if (isNative) {
        const storedStr = await getSecureItem("recording");
        const data = storedStr ? JSON.parse(storedStr) : null;

        if (!data) {
          console.warn("No stored recording data found to resume from.");
          return;
        }

        console.log('📱 Calling native resumeRecording...');

        // This will now create a proper new sequential segment AND restart the timer
        const newSegUri: string = await RecorderModule.resumeRecording();

        recordingRef.current = newSegUri;
        console.log("✅ Resume Recording with new segment:", newSegUri);

        // Update storage with new segment path
        await setSecureItem("recording", JSON.stringify({
          ...(data || {}),
          recording_uri: newSegUri,
          isRecording: true,
          isPaused: false,
          pausedAt: undefined,
        }));

        Sentry.addBreadcrumb({
          category: 'Recording is Resumed',
          message: 'Recording is Resumed - New Segment Created',
          level: 'info',
          data: {
            assesmentId: data?.assesmentId ?? null,
            RecordingUri: newSegUri,
            screenName: "Recording Screen",
            newSegmentCreated: true
          },
        });

        Sentry.captureMessage('Recording is Resumed', {
          level: 'info',
          extra: {
            assesmentId: data?.assesmentId ?? null,
            RecordingUri: newSegUri,
            newSegmentCreated: true
          },
          tags: {
            feature: 'audio-recorder',
            platform: Platform.OS,
            auto_resume_upload: 'true',
          },
        });
      } else {
        // Web / non-native - existing logic
        if (recordingRef.current) {
          await recordingRef.current.recordAsync();
        }
      }

      setIsRecording(true);
      setIsPaused(false);

      ToastService.show({
        message: "Your recording has resumed.",
        type: "info",
        duration: 4000,
        position: "top",
        bottomOffset: 80,
        autoHide: true,
        backgroundColor: theme.colors.buttonColor,
        icon: "checkmark-circle"
      });

    } catch (err) {
      console.error("Failed to resume recording", err);
    }
  };


  const _stopRecording = async () => {
    try {
      console.log("Recording Ref Current :", recordingRef.current)
      if (!recordingRef.current) return null;

      let uri: string | null = null;

      if (Platform.OS === "ios" || Platform.OS === "android") {
        // ✅ This returns the merged final.m4a path
        uri = await RecorderModule.stopRecording();
      } else {
        await recordingRef.current.stopAndUnloadAsync();
        uri = recordingRef.current.getURI();
      }

      if (!uri) return null;

      // Clear local state
      recordingRef.current = null;
      setIsRecording(false);
      setIsPaused(false);
      //setRecordedSegments([]); // no longer needed
      setRecordingURI(uri);    // keep the final path

      return uri;              // ✅ return the actual file path
    } catch (e) {
      console.error('Error stopping recording:', e);
      return null;
    }
  };

  const convertBlobToDataURI = async (blobURI) => {
    const blob = await fetch(blobURI).then((res) => res.blob());
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  };

  const firstRecording = async () => {

    // console.log("recordingURI:" + recordingURI)
    let success = await _startRecording();
    if (success) {
      setIsFirstTimeRecording(false);
      setRecordingURI(null);
    }
  };

  const loadStoredRecordings = async (type: string) => {
    try {
      const result = await RecorderModule.getStoredSegments();
      //console.log('Stored recordings:', result);

      const { segments, totalCount } = result;

      if (segments.length > 0) {
        const recordingData = await getRecordingData();
        let totalDuration = 0;
        if (recordingData) {
          for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            totalDuration += Math.ceil(segment.duration) || 0;
            const segmentIndex = extractIndexFromSegmentName(segment.name);

            // Check if this chunk is already uploaded
            const existingChunk = recordingData.chunks?.find(chunk => chunk.index === segmentIndex);

            if (!existingChunk || existingChunk.status !== 'uploaded') {

              await saveChunkStatus({
                index: segmentIndex,
                path: segment.path,
                name: segment.name,
                size: segment.size
              }, 'pending');
              //}
            }
          }
          if (type === "reload") {
            const storedStr = await getSecureItem("recording");
            if (storedStr) {
              const data = JSON.parse(storedStr);
              setRecordingTime(totalDuration);
              await setSecureItem("recording", JSON.stringify({
                ...data,

                "recordingTime": totalDuration
              }));
            }
          }

        }
      }

      return segments;
    } catch (error) {
      console.error('Failed to load stored recordings:', error);
      return [];
    }
  };

  const _playAudio = async () => {
    try {
      // Unload any previous sound
      if (sound) {
        await sound.unloadAsync();
        setSound(null);
      }

      // Show processing message while finalizing
      setProcessingMessage("Preparing audio for playback...");
      setIsProcessing(true);

      let sourceURI: string | null = recordingURI || null;

      // For native platforms, ensure we have a finalized M4A file
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        // If we don't have a finalized recording or we have segments, finalize first
        const getExt = (u: string) => {
          try { return u.split('?')[0].split('#')[0].split('.').pop()?.toLowerCase() || ''; }
          catch { return ''; }
        };

        // Check if we need finalization
        let needsFinalization = !sourceURI || getExt(sourceURI) === 'caf';

        // Also check if we have an existing final.m4a vs segments
        if (!needsFinalization && sourceURI) {
          const isM4A = getExt(sourceURI) === 'm4a';
          if (!isM4A) {
            needsFinalization = true;
          }
        }

        if (needsFinalization) {
          console.log("Finalizing recording for playback...");

          const storedStr = await getSecureItem("recording");
          let pathToFinalize: string | null = sourceURI;

          if (storedStr) {
            const data = JSON.parse(storedStr);
            pathToFinalize = sourceURI || data.recording_uri || failedRecordingUri || null;
          }

          if (!pathToFinalize) {
            setIsProcessing(false);
            setProcessingMessage("");
            console.error("No recording path found for playback");
            return;
          }

          try {
            // Finalize all segments into a single M4A file
            const finalizedURI = await RecorderModule.finalizeFromAnyPath(pathToFinalize);
            console.log("Finalized for playback:", finalizedURI);

            if (finalizedURI) {
              sourceURI = finalizedURI;
              // Cache the finalized URI for future playbacks
              setRecordingURI(finalizedURI);
            }
          } catch (e) {
            setIsProcessing(false);
            setProcessingMessage("");
            console.error("Failed to finalize recording for playback:", e);
            Alert.alert('Playback Error', 'Could not prepare audio for playback. Please try again.');
            return;
          }
        }
      }

      if (!sourceURI) {
        setIsProcessing(false);
        setProcessingMessage("");
        console.error("No audio source available for playback");
        return;
      }

      // For web: convert blob to data URI if needed
      if (Platform.OS === 'web') {
        sourceURI = await convertBlobToDataURI(sourceURI);
      }

      setProcessingMessage("Loading audio...");

      // Configure audio and play
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
        // playThroughEarpieceAndroid: false, // uncomment if you want speaker by default
      });

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: sourceURI },
        { shouldPlay: true, progressUpdateIntervalMillis: 250 },
        onPlaybackStatusUpdate
      );

      setSound(newSound);
      setIsPlaying(true);
      setIsProcessing(false);
      setProcessingMessage("");

      // Optional: Inspect status
      const status = await newSound.getStatusAsync();
      console.log("Playback status:", status);

      await newSound.playAsync();
      console.log("Playing finalized audio:", sourceURI);

    } catch (error) {
      setIsProcessing(false);
      setProcessingMessage("");
      console.error("Error playing audio:", error);
      Alert.alert('Playback Error', 'Failed to play audio. Please try again.');
    }
  };

  // Add a function to handle playback status updates
  const onPlaybackStatusUpdate = (status) => {
    if (status.isLoaded) {
      setPlaybackPosition(status.positionMillis);

      if (status.didJustFinish) {
        // Audio finished playing
        setIsPlaying(false);
        setPlaybackPosition(0);
      }
    }
  };

  // Add function to pause audio playback
  const _pauseAudio = async () => {

    if (sound && isPlaying) {
      console.log("Pausing audio playback...");
      await sound.pauseAsync();
      setIsPlaying(false);
    }
  };

  const onModalCancel = () => {
    setShowModal(false);
    setRestartLoading(false);
    setCancelRecording(true);
    // setShowPauseResume(false);
  };

  const onRestartModalCancel = () => {
    setRestartLoading(false);
  };

  const restartRecording = () => {
    setRestartLoading(true);
    setShowModal(false);
  };

  const onRestartRecording = async () => {
    setRestartLoading(false);
    setProcessingMessage("Stopping active recording...");
    setIsProcessing(true);
    let success = await _stopRecording()
    setIsProcessing(false);
    setProcessingMessage("");

    if (success) {
      await removeSecureItem("recording");
      setIsFirstTimeRecording(true);
      setRecordingURI(null);
      setRecordingTime(0);
      setCancelRecording(false);
      setRestartLoading(false);
      setShowPauseResume(false);
      console.log("----On Restart Recording-----: false");
      setSound(null);
    }
  };

  // Function to toggle checkbox state
  const toggleCheckbox = (categoryIndex, questionId) => {
    const updatedCategories = [...categories];
    const questionIndex = updatedCategories[categoryIndex].items.findIndex(q => q.questionCode === questionId);

    if (questionIndex !== -1) {
      let isChecked = !updatedCategories[categoryIndex].items[questionIndex].checked;
      updatedCategories[categoryIndex].items[questionIndex].checked = isChecked;

      let count = checkedCount;
      if (isChecked) {
        count++;
      } else {
        count--;
      }

      if (totalQuestions != 0 && count != 0) {
        setProgress((count / totalQuestions) * 100);
      } else {
        setProgress(0);
      }

      setCheckedCount(count);
      setCategories(updatedCategories);
    }
  };


  const callServer = async () => {
    try {
      // setIsUploading(true);
      // setUploadProgress(0);

      // console.log("pppppppppp", recording_uri);

      // await setSecureItem("recording", JSON.stringify({
      //   "assesmentId": assesmentId,
      //   "recording_uri": recording_uri,
      //   "email": await getSecureItem("email"),
      //   "uploadFailed": true
      // }));

      // const fileName = recording_uri.split('/').pop() || 'recording.m4a';
      // setRecordingFileName(fileName);

      // let result = await uploadRecord(recording_uri, assesmentId, (progress) => {
      //   console.log("Upload progress:", progress + "%");
      //   const cappedProgress = Math.min(progress, 100);
      //   setUploadProgress(cappedProgress);
      // });
      const chunks = await getUploadedChunks();
      const lastChunk = chunks[chunks.length - 1];
     
      console.log("lastChunk", JSON.stringify(lastChunk))
      const result = await uploadFinalChunk(lastChunk.path, assesmentId, lastChunk.index, lastChunk.segmentName);
      console.log("Upload successful:", result);
      if (result.status == "ok") {
        // Success handling
         await _stopRecording();
        try {
          //const finalizedDuration = await RecorderModule.getAudioDurationFromPath(recording_uri);
          Sentry.addBreadcrumb({
            category: 'recording uoploaded successfully',
            message: 'recording uoploaded successfully',
            level: 'info',
            data: {
              assesmentId: assesmentId ?? null,
              response: result,
              //timeFromSegment: finalizedDuration
            },
          });

          Sentry.captureMessage('recording uoploaded successfully', {
            level: 'info',
            extra: {
              assesmentId: assesmentId ?? null,
              response: result
            },
            tags: {
              feature: 'audio-recorder',
              platform: Platform.OS,
              auto_resume_upload: 'true',
            },
          });
        } catch (e) {
          console.log("error while removing getting the file size", e)
        }
        await removeSecureItem("recording");
        // if (Platform.OS === "ios") {
        //   await RecorderModule.deleteFile(recording_uri);

        // }
        // else if (Platform.OS === "android") {
        //   await RecorderModule.deleteFile(recording_uri);
        // }
        ToastService.show({
          message: "Hold tight, Goodly AI is processing your request.",
          type: "info",
          duration: 4000,
          position: "bottom",
          bottomOffset: 80,
          autoHide: true,
          backgroundColor: theme.colors.buttonColor,
          onPress: () => {
            console.log("Toast clicked");
          },
          customContent: (
            <View style={{ height: 40, width: 40, backgroundColor: "#fff", borderRadius: 25, justifyContent: "center", alignItems: "center" }}>
              <Image
                source={require('assets/images/sparkles.png')}
              />
            </View>
          ),
        });

        dispatch(hideRecorder());

        setTimeout(() => {
          console.log(" --- Navigating to Dashboard ---");
          navigation.navigate('Dashboard');
        }, 100);
      } else {
        Sentry.addBreadcrumb({
          category: 'recording uoploaded error',
          message: 'recording uoploaded error',
          level: 'info',
          data: {
            assesmentId: assesmentId ?? null,
            response: result,
            error: result.errorMessage || 'Upload failed'
          },
        });
        console.log("Upload error:");

        setUploadError(result.errorMessage || 'Upload failed');
        // setFailedRecordingUri(recording_uri);
        // Alert.alert(result.errorMessage);
      }
    } catch (error) {
      console.log("Upload error:", error);
      setUploadError(error.message || 'Network error occurred');
      // Alert.alert('Upload Error', 'Failed to upload recording. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setRecordingURI(null);
    }
  };

  const retryUpload = async () => {
    try {
      const storedRecording = await getSecureItem("recording");
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);
        if (recordingData.email == await getSecureItem("email")) {
          setUploadError(null);


          await submitToAI();
        }
      } else {
        dispatch(hideRecorder());
      }
    } catch (error) {
      console.error("Retry failed:", error);
      Alert.alert('Retry Failed', 'Could not retry upload. Please restart recording.');
    }
  };

  const submitToAI = async () => {
    console.log("\n\n ------- Submitting to AI... \n\n");
    setShowModal(false);
    try {
      setProcessingMessage("Stopping recording...");
      setIsProcessing(true);
      const segments = await loadStoredRecordings("submit");
      
      console.log("Recording stopped");

      setProcessingMessage("Loading recording segments...");
     
      console.log("Loaded segments:", segments.length);

      setProcessingMessage("Checking for unprocessed chunks...");
      const pendingChunks = await getPendingChunks();
      const failedChunks = await getFailedChunks();

      const totalUnprocessedChunks = pendingChunks.length + failedChunks.length;
      console.log(`Found ${totalUnprocessedChunks} unprocessed chunks (${pendingChunks.length} pending, ${failedChunks.length} failed)`);

      if (totalUnprocessedChunks > 0) {
        setProcessingMessage(`Processing ${totalUnprocessedChunks} remaining chunks...`);

        // Check network connectivity first
        const netInfo = await NetInfo.fetch();
        if (!netInfo.isConnected) {
          throw new Error('No internet connection. Please check your network and try again.');
        }
        await uploadAllPendingChunks()
      }


      setProcessingMessage("Preparing final upload...");
      setIsProcessing(false);
      setProcessingMessage("");
      
      await callServer();

    } catch (error) {
      console.error("Error in submitToAI:", error);
      setIsProcessing(false);
      setProcessingMessage("");
     
      // Set upload error as requested
      setUploadError(error || 'Failed to process recording. Please try again.');
    }
  };

// const waitForChunkCompletion = async (maxWaitMinutes = 3) => {
//   const maxWaitTime = maxWaitMinutes * 60 * 1000;
//   const checkInterval = 3000; // Check every 3 seconds
//   let elapsed = 0;
  
//   while (elapsed < maxWaitTime) {
//     const pendingChunks = await getPendingChunks();
//     const failedChunks = await getFailedChunks();
    
//     // Actually completed - no pending or failed chunks
//     if (pendingChunks.length === 0 && failedChunks.length === 0) {
//       return { success: true, message: 'All chunks uploaded successfully' };
//     }
    
//     // Still have failed chunks that aren't being retried
//     if (failedChunks.length > 0 && !isProcessingChunks) {
//       processChunkUploadQueue(); 
//     }
    
//     await new Promise(resolve => setTimeout(resolve, checkInterval));
//     elapsed += checkInterval;
//   }
  
//   // Timeout reached
//   const finalPending = await getPendingChunks();
//   const finalFailed = await getFailedChunks();
  
//   return { 
//     success: false, 
//     message: `Timeout: ${finalPending.length} pending, ${finalFailed.length} failed chunks remain`,
//     timeout: true
//   };
// };

const uploadAllPendingChunks = async () => {
  try {
    // Get all pending and failed chunks
    const pendingChunks = await getPendingChunks();
    const failedChunks = await getFailedChunks();
    
    const allChunksToUpload = [...pendingChunks, ...failedChunks];
    
    if (allChunksToUpload.length === 0) {
      console.log("No chunks to upload");
      return true; // No chunks to upload = success
    }
    
    console.log(`Found ${allChunksToUpload.length} chunks to upload`);
    
    let successCount = 0;
    let failedCount = 0;
    
    // Upload each chunk once (no retries)
    for (const chunk of allChunksToUpload) {
      try {
        console.log(`Uploading chunk ${chunk.index} (${chunk.segmentName})`);
        
        // Mark as uploading
        await saveChunkStatus({
          index: chunk.index,
          path: chunk.path,
          name: chunk.segmentName,
          size: chunk.size || 0
        }, 'uploading');
        
        // Upload chunk
        const result = await uploadChunk(chunk.path, assesmentId, chunk.index, chunk.segmentName);
        
        if (result.status === "ok") {
          await markChunkAsUploaded(chunk.index);
          successCount++;
          console.log(`Chunk ${chunk.index} uploaded successfully`);
        } else {
          await addToFailedChunks({ ...chunk, error: result.errorMessage || 'Upload failed' });
          failedCount++;
          console.log(`Chunk ${chunk.index} failed: ${result.errorMessage}`);
        }
        
      } catch (error) {
        await addToFailedChunks({ ...chunk, error: error.message });
        failedCount++;
        console.log(`Chunk ${chunk.index} failed: ${error.message}`);
      }
    }
    
    console.log(`Upload complete: ${successCount} success, ${failedCount} failed`);
    
    // Return true only if all chunks uploaded successfully
    return failedCount === 0;
    
  } catch (error) {
    console.error('Error in uploadAllPendingChunks:', error);
    return false;
  }
};
  const stopRecording = async () => {
    // _stopRecording();
    if (!isPaused) {
      await _pauseRecording()
    }
    setShowModal(true);
    // setShowPauseResume(false);
  };

  const calculateUnanswered = () => {
    return totalQuestions - checkedCount;
  };


  const renderWebContent = () => {
    return (
      <div
        data-testid="visit-scrollable-container"
        style={{
          overflowY: 'auto',
          height: '100%',
          paddingBottom: FOOTER_HEIGHT,
        }}
      >
        {categories.map((category, categoryIndex) => (
          <View key={categoryIndex} style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>{category?.container.subHeading}</Text>

            {category.items.map((question) => {
              return (
                <>

                  {/* {!question.isContainer && */}
                  {!question.isContainer && question.isDisplayCheatSheet &&
                    <TouchableOpacity
                      key={question.questionCode}
                      style={styles.questionRow}
                      onPress={() => toggleCheckbox(categoryIndex, question.questionCode)}
                      activeOpacity={0.7}
                    >
                      <View style={[
                        styles.checkbox,
                        question.checked ? styles.checkboxChecked : styles.checkboxUnchecked
                      ]}>
                        {question.checked && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </View>
                      <Text style={styles.questionText}>{question.description}</Text>
                    </TouchableOpacity>
                  }
                </>
              )
            }
            )}

            {categoryIndex < categories.length - 1 && <View style={styles.divider} />}
          </View>
        ))}
      </div>
    )
  }

  const renderNativeContent = () => {
    return (
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: FOOTER_HEIGHT }}
      >
        {categories.map((category, categoryIndex) => (
          <View key={categoryIndex} style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>{category?.container.subHeading}</Text>

            {category.items.map((question) => {
              return (
                <>
                  {!question.isContainer && question.isDisplayCheatSheet &&
                    <TouchableOpacity
                      key={question.questionCode}
                      style={styles.questionRow}
                      onPress={() => toggleCheckbox(categoryIndex, question.questionCode)}
                      activeOpacity={0.7}
                    >
                      <View style={[
                        styles.checkbox,
                        question.checked ? styles.checkboxChecked : styles.checkboxUnchecked
                      ]}>
                        {question.checked && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </View>
                      <Text style={styles.questionText}>{question.description}</Text>
                    </TouchableOpacity>
                  }
                </>
              )
            }
            )}

            {categoryIndex < categories.length - 1 && <View style={styles.divider} />}
          </View>
        ))}
      </ScrollView>
    )
  }
  const UploadProgressBar = ({ progress, error, fileName, recordingUri, onRetry }) => {
    if (error) {
      return (
        <View style={styles.uploadContainer}>

          {isOffline ?
            <Text style={styles.fileName}>{UPLOAD_MINI_ERROR}</Text> : <Text style={styles.fileName}>{UPLOAD_MINI_ERROR_WITH_INTERNET}</Text>}

          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Text style={styles.retryButtonText}>Retry Submission</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.uploadContainer}>
        <View style={styles.uploadHeader}>
          <Text style={styles.uploadTitle}>Upload in Progress</Text>
          <Text style={[styles.uploadPercentage, { color: theme.colors.buttonColor }]}>
            {progress}%
          </Text>
        </View>

        <View style={styles.progressBarContainerUpload}>
          <Animated.View
            style={[
              styles.progressBarUpload,
              {
                width: `${progress}%`,
                backgroundColor: theme.colors.buttonColor,
                ...(Platform.OS !== 'web' && {
                  transform: [{ scaleX: progress / 100 }],
                  transformOrigin: 'left'
                })
              }
            ]}
          />
        </View>

        <Text style={styles.uploadSubtext}>
          {progress < 100
            ? "Please wait while your recording is being processed..."
            : "Finalizing upload..."
          }
        </Text>
      </View>
    );
  };
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        if (isExpanded) {
          // If the recorder is expanded, minimize it
          // toggleExpand();
          if (showPauseResume || recordingURI != null) {
            // Recording in progress or completed
            console.log("Recording is active or completed");
            toggleExpand();
          } else {
            // No active recording
            console.log("No active recording");
            dispatch(hideRecorder());
          }
          return true; // Prevent default back behavior
        }
        // Otherwise let the default back action occur
        return false;
      };

      // Add event listener for hardware back button (Android)
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      // For web browser back button
      if (Platform.OS === 'web') {
        const handlePopState = () => {
          if (isExpanded) {
            if (showPauseResume || recordingURI != null) {
              // Recording in progress or completed
              console.log("Recording is active or completed");
              toggleExpand();
            } else {
              // No active recording
              console.log("No active recording");
              dispatch(hideRecorder());
            }
          }
        };

        window.addEventListener('popstate', handlePopState);

        return () => {
          window.removeEventListener('popstate', handlePopState);
        };
      }

      // Clean up the event listener
      const backHandler = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => backHandler.remove();
    }, [isExpanded, showPauseResume, recordingURI])
  );

  // Touch handlers for the bottom sheet
  const handleTouchStart = (e) => {
    if (Platform.OS === 'web') {
      startY.current = e.nativeEvent.pageY;
      isDragging.current = true;
    }
  };

  const handleTouchMove = (e) => {
    if (Platform.OS === 'web' && isDragging.current) {
      currentY.current = e.nativeEvent.pageY;
      const deltaY = currentY.current - startY.current;

      // Only allow dragging down, not up (when already at top)
      if (deltaY > 0) {
        translateYAnim.setValue(deltaY);

        // Gradually reduce backdrop opacity as we drag down
        const newOpacity = Math.max(0, 0.5 - (deltaY / (screenHeight * 2)));
        backdropOpacity.setValue(newOpacity);
      }
    }
  };

  const handleTouchEnd = () => {
    if (Platform.OS === 'web' && isDragging.current) {
      isDragging.current = false;

      // If dragged more than 30% of screen height, close the sheet
      if (currentY.current - startY.current > screenHeight * 0.3) {
        //console.log("going to toggle ")
        // toggleExpand();
        goBack()
      } else {
        // Otherwise snap back to open position
        Animated.parallel([
          Animated.spring(translateYAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 65,
          }),
          Animated.timing(backdropOpacity, {
            toValue: 0.5,
            duration: 300,
            useNativeDriver: true,
          })
        ]).start();
      }
    }
  };

  // const closeRecording = async () => {
  //   if (uploadError) {


  //     const storedRecording = await getSecureItem("recording");
  //     console.log("storedRecording-->", storedRecording)
  //     if (storedRecording) {
  //       const recordingData = JSON.parse(storedRecording);
  //       if (recordingData.email == await getSecureItem("email")) {

  //         await removeSecureItem("recording");
  //         await RecorderModule.deleteFile(recordingData.recording_uri);
  //       }
  //       dispatch(hideRecorder());
  //     }
  //   } else {
  //     let success = await _stopRecording()
  //     console.log("----Close Recording----->");
  //     if (success) {
  //       setRecordingURI(null)
  //       //dispatch(hideRecorder());
  //       const storedRecording = await getSecureItem("recording");
  //       if (storedRecording) {
  //         const recordingData = JSON.parse(storedRecording);
  //         if (recordingData.email == await getSecureItem("email")) {

  //           await removeSecureItem("recording");
  //           await RecorderModule.deleteFile(recordingData.recording_uri);
  //         }
  //         dispatch(hideRecorder());
  //       }
  //     }
  //   }
  // }

  const closeRecording = async () => {
    setShowDeleteModal(false);
    try {
      // Try to stop if there might be an active native recorder
      try {
        // _stopRecording() in your code returns a path or true; ignore result here,
        // we’re discarding anyway. If it throws, we keep going.
        setProcessingMessage("Stopping active recording...");
        setIsProcessing(true);
        let success = await _stopRecording();

        if (success) {
          setRecordingURI(null)
          dispatch(hideRecorder());
        }
      } catch (_) { }

      // Load secure recording state (may be null after crashes)
      const stored = await getSecureItem("recording");
      const myEmail = await getSecureItem("email");

      if (stored) {
        await RecorderModule.clearStoredSegments();
        const data = JSON.parse(stored);
        // Only clean up if the session belongs to this user/email (your original check)
        if (!myEmail || data.email === myEmail) {
          const currentUri: string | null = data.recording_uri || null;
          const segs: string[] = Array.isArray(data.segments) ? data.segments : [];

          // Prefer native full-session delete (one call)
          if (RecorderModule?.deleteSessionForPath && currentUri) {
            // try {
            //   // You’ll implement this native helper to remove the whole session folder.
            //   await RecorderModule.deleteSessionForPath(currentUri);
            // } catch (e) {
            //   console.warn("deleteSessionForPath failed; falling back to per-file delete:", e);
            //   // Fall through to fallback below
            // }
          }

          //  Fallback: delete per-file (final + all segs)
          // Build a de-duped list of files to delete
          const uniqueFiles = new Set<string>();
          if (currentUri) uniqueFiles.add(currentUri);
          for (const s of segs) if (s) uniqueFiles.add(s);

          // Best-effort: also try final.m4a in the same folder as currentUri
          if (currentUri) {
            try {
              const dir = currentUri.replace(/\/[^/]+$/, ""); // naive parent
              uniqueFiles.add(`${dir}/final.m4a`);
            } catch { }
          }

          // Delete each file if the one-shot session delete wasn't available
          if (!RecorderModule?.deleteSessionForPath) {
            for (const f of uniqueFiles) {
              try {
                //  await RecorderModule.deleteFile(f);
              } catch (e) {
                // ignore individual failures; continue deleting the rest
                console.warn("deleteFile failed for", f, e);
              }
            }
          }
          await removeSecureItem("recording");
        }
      }
      setIsProcessing(false);
      setProcessingMessage("");
      // Clear any React state & hide UI
      setRecordingURI(null);
      dispatch(hideRecorder());
    } catch (err) {
      console.error("closeRecording failed:", err);
      // Make sure UI still closes even if cleanup partially fails
      setRecordingURI(null);
      dispatch(hideRecorder());
    }
  };
  const toggleExpand = () => {
    // console.log("Expnded : "+isExpanded)
    setIsExpanded(!isExpanded);
    dispatch(toggleExpanded());
  };

  const goBack = () => {
    console.log("Clicking on go back")
    if (showPauseResume || recordingURI != null || uploadError) {
      toggleExpand()
    } else {
      dispatch(hideRecorder())
    }
  }
  const onMessageModalCancel = () => {
    setShowMessageModal(false);
  };
  const onDeleteModalCancel = () => {
    setShowDeleteModal(false);
  }
  const openDeleteModal = () => {
    console.log("Open Delete Modal:", showPauseResume, recordingURI)
    if (showPauseResume || recordingURI != null || uploadError) {
      setShowDeleteModal(true);
    } else {
      dispatch(hideRecorder())
    }
  }
  if (!isExpanded) {
    return (
      <LinearGradient colors={['#4F47E5', '#6366F1']} style={styles.miniPlayerGradient}>
        <View style={miniStyles.miniPlayer}>
          {/* <DynamicMessageModalWithConfirm visible={showMessageModal} onCancel={onMessageModalCancel} message={message} title={msgTitle} iconComponent={<MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" />} /> */}
          {(uploadError || isUploading) ? (
            <>
              {uploadError ? (
                <>
                  <TouchableOpacity onPress={retryUpload}>
                    <FontAwesome name="refresh" size={20} color="#fff" />
                  </TouchableOpacity>
                  {isOffline ?
                    <View style={{ flex: 1 }}>
                      <Text style={miniStyles.miniMsg}>{UPLOAD_MINI_ERROR}</Text>
                    </View> :
                    <View style={{ flex: 1 }}>
                      <Text style={miniStyles.miniMsg}>{UPLOAD_MINI_ERROR_WITH_INTERNET}</Text>
                    </View>
                  }
                </>
              ) : (
                <View style={{ flex: 1 }}>
                  <Text style={miniStyles.miniMsg}>Uploading recording {uploadProgress}%</Text>
                </View>
              )}
            </>
          ) : isProcessing ? (
            // Add processing state for mini player
            <>
              <ActivityIndicator size={20} color="#fff" />
              <View style={{ flex: 1 }}>
                <Text style={miniStyles.miniMsg}>{processingMessage}</Text>
              </View>
            </>
          ) : (
            <>
              <TouchableOpacity
                onPress={
                  isRecording
                    ? _pauseRecording
                    : isPaused
                      ? _resumeRecording
                      : firstRecording
                }
              >
                {isRecording ? (
                  <FontAwesome name="pause" size={20} color="#fff" />
                ) : (
                  <FontAwesome name="play" size={20} color="#fff" />
                )}
              </TouchableOpacity>


              <Text style={miniStyles.miniTimer}>{formatTime(recordingTime)}</Text>
            </>
          )}
          <TouchableOpacity onPress={toggleExpand}>

            <Feather name="chevron-up" size={28} color="#fff" />

          </TouchableOpacity>
        </View>
      </LinearGradient>
    );
  }


  return (
    <>
      {/* Backdrop overlay */}
      <Animated.View
        style={[
          styles.backdrop,
          { opacity: backdropOpacity }
        ]}
        onTouchStart={() => toggleExpand()}
      />

      {/* Bottom Sheet */}
      <Animated.View
        style={[
          styles.bottomSheet,
          {
            transform: [{ translateY: translateYAnim }],
          }
        ]}
      // onTouchStart={handleTouchStart}
      // onTouchMove={handleTouchMove}
      // onTouchEnd={handleTouchEnd}
      >
        <FullScreenLoader visible={showModal} onCancel={onModalCancel} onConfirm={submitToAI} value={calculateUnanswered()} />
        <RestartRecordModal visible={restartLoading} onCancel={onRestartModalCancel} onConfirm={onRestartRecording} />
        <RecordDeleteModal visible={showDeleteModal} onCancel={onDeleteModalCancel} onConfirm={closeRecording} />
        <DynamicMessageModalWithConfirm visible={showMessageModal} onCancel={onMessageModalCancel} message={NO_SPACE_ERROR} title={NO_SPACE_ERROR_TITLE} iconComponent={<MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" />} />

        <MicrophonePermissionModal
          visible={showMicrophoneModal}
          onCancel={onMicrophoneModalCancel}
          onSettings={onMicrophoneModalSettings}
        />

        {/* Pull handle */}
        <TouchableOpacity style={styles.handleContainer} onPress={goBack}>
          <View style={styles.handle} />
        </TouchableOpacity>

        {/* Header */}
        <View style={styles.sheetHeader}>
          <TouchableOpacity style={styles.backButton} onPress={goBack}>
            <Ionicons name="chevron-back" size={24} color="black" />
          </TouchableOpacity>
          <Text allowFontScaling={false} style={styles.headerTitle}>Recording in Progress</Text>
          <TouchableOpacity onPress={openDeleteModal} style={styles.closeButton}>
            {/* <X size={24} color="#000" /> */}
            <Ionicons name="close" size={24} color="#000" />
          </TouchableOpacity>
        </View>

        {/* Progress Section */}
        {!loading && categories.length > 0 && (
          <View style={styles.progressSection}>
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>Questions</Text>
              <Text style={styles.progressCount}>{checkedCount}/{totalQuestions}</Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBar, { width: `${progress}%` }]} />
            </View>
          </View>
        )}

        {/* Content */}
        <View style={styles.sheetContent}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#4F47E5" />
              <Text style={styles.loadingText}>Loading questions...</Text>
            </View>
          ) : categories.length > 0 ? (
            Platform.OS === 'web' ? renderWebContent() : renderNativeContent()
          ) : (
            <View style={styles.blankTextContainer}>
              <Text style={styles.blankText}>No Question available</Text>
            </View>
          )}
        </View>

        {/* Footer */}
        <View style={styles.sheetFooter}>
          {(isUploading || uploadError) ? (
            <UploadProgressBar
              progress={uploadProgress}
              error={uploadError}
              fileName={recordingFileName}
              recordingUri={failedRecordingUri}
              onRetry={retryUpload}
            />
          ) : (
            <View style={styles.timerContainer}>
              {/* <AudioPickButton
                onPicked={(f) => {
                  if (!f) return;
                  console.log("Picked file :", f.uri)
                  uploadRecord(f.uri, assesmentId, (progress) => {
                    console.log("Upload progress:", progress + "%");
                    const cappedProgress = Math.min(progress, 100);
                    setUploadProgress(cappedProgress);
                  })
                  //     let result = await uploadRecord(recording_uri, assesmentId, (progress) => {
                  //   console.log("Upload progress:", progress + "%");
                  //   const cappedProgress = Math.min(progress, 100);
                  //   setUploadProgress(cappedProgress);
                  // });
                }}
              /> */}
              <Text style={styles.timerText}>{formatTime(recordingTime)}</Text>
              {isProcessing ? (
                <View style={styles.processingControlsContainer}>
                  <ActivityIndicator size="large" color={theme.colors.buttonColor} />
                  <Text style={styles.processingControlsText}>{processingMessage}</Text>
                </View>
              ) : (
                <View style={styles.controlsContainer}>
                  {isFirstTimeRecording ? (
                    <TouchableOpacity style={styles.startButton} onPress={firstRecording}>
                      <View style={styles.startButtonInner}>
                        <FontAwesome name="microphone" size={20} color="#ffffff" />
                        <Text style={styles.startText}>Start Recording</Text>
                      </View>
                    </TouchableOpacity>
                  ) : (
                    <>
                      {/* Pause/Resume Button */}
                      {showPauseResume &&
                        <Pressable style={styles.controlButton} onPress={
                          isRecording
                            ? _pauseRecording
                            : isPaused
                              ? _resumeRecording
                              : _startRecording
                        }>
                          <View style={styles.pauseButton}>
                            {isRecording &&
                              <FontAwesome name="pause" size={18} color="#ffffff" />
                            }
                            {isPaused &&
                              <FontAwesome name="play" size={18} color="#ffffff" />
                            }
                          </View>
                        </Pressable>
                      }
                      {/* Restart Button */}
                      {cancelRecording && (
                        // <TouchableOpacity style={styles.controlButton} onPress={restartRecording}>
                        <TouchableOpacity style={styles.controlButton} onPress={_playAudio}>
                          <Feather name="refresh-ccw" size={28} color="#ffffff" />
                        </TouchableOpacity>
                      )}
                      {/* Stop/Submit Button */}
                      <TouchableOpacity style={styles.controlButton} onPress={stopRecording}>
                        <FontAwesome name="check" size={18} color="#ffffff" />
                      </TouchableOpacity>

                    </>
                  )}
                </View>
              )}
            </View>
          )}
        </View>
      </Animated.View>
    </>
  );
};

const miniStyles = StyleSheet.create({
  miniPlayer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
  },
  miniTimer: {
    color: '#fff',
    fontSize: 20,
    fontFamily: 'Poppins_600SemiBold',
  },
  miniMsg: {
    color: '#fff',
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: 10
  },
  flipIcon: {
    transform: [{ rotateX: '180deg' }],
  },
});

const styles = StyleSheet.create({
  // Mini player styles
  miniPlayerGradient: {
    height: MINI_PLAYER_HEIGHT,
    width: '100%',
    borderRadius: 8,
    // borderTopLeftRadius: 16,
    // borderTopRightRadius: 16,
    justifyContent: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  errorTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#FF6B6B',
    marginLeft: 8,
  },
  fileName: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#FF6B6B',
    marginBottom: 5,
    textAlign: 'center',
  },
  recordingUri: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#888',
    marginBottom: 8,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  errorMessage: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#FF6B6B',
    textAlign: 'center',
    marginBottom: 15,
  },
  retryButton: {
    backgroundColor: theme.colors.buttonColor,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 24,
    alignItems: 'center',
  },
  retryButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },

  backButton: {
    marginRight: 8,
  },
  // Bottom sheet styles
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
    ...(Platform.OS == 'android' && {
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,

    }),

    zIndex: 998,
  },
  bottomSheet: {
    ...(Platform.OS !== 'android' && {
      position: 'absolute',

    }),
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#f6f6f6ff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: screenHeight - 100, // Slightly smaller than full screen to show it's a sheet
    zIndex: 999,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 20,
    overflow: 'hidden',
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  handle: {
    width: 40,
    height: HANDLE_HEIGHT,
    borderRadius: 2.5,
    backgroundColor: '#E0E0E0',
  },
  sheetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 15,

    // borderBottomWidth: 1,
    // borderBottomColor: '#EAEAEA',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_500Medium',
    color: '#000',
  },
  closeButton: {
    padding: 5,
  },
  sheetContent: {
    flex: 1,
  },
  sheetFooter: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    // borderTopWidth: 1,
    // borderTopColor: '#EAEAEA',
    // paddingVertical: 15,
    paddingBottom: 15,
    paddingHorizontal: 20,
    minHeight: FOOTER_HEIGHT,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    // Shadow for Android
    elevation: 5,
  },

  // Progress section
  progressSection: {
    paddingVertical: 10,
    // backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#000',
    fontFamily: 'Poppins_400Regular',
  },
  progressCount: {
    fontSize: 14,
    color: '#000',
    fontFamily: 'Poppins_400Regular',
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: '#EAEAEA',
    borderRadius: 10,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#0079FE',
    borderRadius: 10,
  },
  progressBarContainerUpload: {
    height: 8,
    backgroundColor: '#EAEAEA', // Light green background
    borderRadius: 4,
    width: '100%', // Full width
    marginBottom: 15,
  },
  progressBarUpload: {
    height: 6,
    backgroundColor: '#0079FE',
    borderRadius: 10,
  },

  // Content
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: 10,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    color: '#4F47E5',
  },
  scrollView: {
    flex: 1,
  },
  categoryContainer: {
    paddingHorizontal: 20,
  },
  categoryTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 12,
    marginTop: 18,
    color: '#000',
  },
  questionRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: "center"
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkboxChecked: {
    backgroundColor: '#007AFF',
  },
  checkboxUnchecked: {
    borderWidth: 1,
    borderColor: '#999',
  },
  questionText: {
    fontSize: 14,
    flex: 1,
    fontFamily: 'Poppins_500Medium',
    color: '#000',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  blankTextContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blankText: {
    textAlign: 'center',
    fontSize: 20,
    fontFamily: 'Poppins_500Medium',
  },

  // Footer
  timerContainer: {
    alignItems: 'center',
  },
  timerText: {
    fontSize: 30,
    marginBottom: 10,
    fontFamily: 'Poppins_500Medium',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.buttonColor,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  pauseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
  },
  startButton: {
    // width: 198,
    // height: 46,
    backgroundColor: theme.colors.buttonColor,
    borderRadius: 40,
    color: '#FFFFFF',
  },
  startButtonInner: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 15,
    paddingVertical: 14,
    paddingHorizontal: 20
  },
  startText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
  },

  // Modal styles
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.95,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,

  },
  iconContainer: {
    marginBottom: 25,
  },
  title: {
    color: '#000',
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 24,
    fontStyle: 'normal',
  }, icon: {
    width: 60,
    height: 60,
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  message: {
    color: '#7C7887',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: '400',
    marginTop: 10,
    marginBottom: 15
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },

  // progressbar
  uploadContainer: {
    alignItems: 'center',
    paddingTop: 10,
    // paddingVertical: 20,
  },
  uploadHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 15,
  },
  uploadTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#000',
  },
  uploadPercentage: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: theme.colors.buttonColor,
  },
  uploadProgressBar: {
    height: 8,
    backgroundColor: theme.colors.buttonColor,
    borderRadius: 4,
    transition: 'width 0.3s ease', // Smooth animation on web
  },
  uploadSubtext: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7C7887',
    textAlign: 'center',
    marginTop: 10,
  },
  disabledButton: {
    opacity: 0.5,
  },
  processingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  processingContainer: {
    backgroundColor: '#fff',
    padding: 30,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 200,
  },
  processingText: {
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    color: '#000',
    marginTop: 15,
    textAlign: 'center',
  },
  processingSubtext: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7C7887',
    marginTop: 8,
    textAlign: 'center',
  },
  processingControlsContainer: {
    alignItems: 'center',
    //paddingVertical: 10,
  },
  processingControlsText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7C7887',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default AudioRecorder;