import React from "react";
import { Button } from "react-native";
import { pickAudio, type PickedAudio } from "./pickAudio";

export default function AudioPickButton({
  onPicked,
  title = "Pick Audio",
}: {
  onPicked: (file: PickedAudio | null) => void;
  title?: string;
}) {
  return (
    <Button
      title={title}
      onPress={async () => {
        const file = await pickAudio();
        onPicked(file); // you'll handle upload/navigation/etc.
      }}
    />
  );
}