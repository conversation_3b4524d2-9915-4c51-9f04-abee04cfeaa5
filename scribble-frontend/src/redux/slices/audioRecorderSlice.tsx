import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isVisible: false,
  isExpanded: false,
  isRecording: false,
  isPaused: false,
  recordingTime: 0,  
  showPauseResume:false,
  recordingURI: null,
  assesId: null
};

const audioRecorderSlice = createSlice({
  name: 'audioRecorder',
  initialState,
  reducers: {
    showRecorder: (state, action) => {
      state.isVisible = true;
      state.isExpanded = action.payload.expanded || false;
      if (action.payload.assesId) {
        state.assesId = action.payload.assesId;
      }
    },
    hideRecorder: (state) => {
      state.isVisible = false;
      state.isExpanded = false;
      state.recordingURI = null;
      state.showPauseResume=false;
      resetRecorder()
    },
    setRecordingState: (state, action) => {
      if (action.payload.isRecording !== undefined) {
        state.isRecording = action.payload.isRecording;
      }
      if (action.payload.recordingURI !== undefined) {
        state.recordingURI = action.payload.recordingURI;
      }
      if (action.payload.showPauseResume !== undefined) {
        state.showPauseResume = action.payload.showPauseResume;
      }
    },
    toggleExpanded: (state) => {
      state.isExpanded = !state.isExpanded;
    },
    setAssesId: (state, action) => {
      state.assesId = action.payload;
    },
    resetRecorder: (state) => {
      return initialState;
    },
    setRecordingTime: (state, action) => {
  state.recordingTime = action.payload;
},
setPausedState: (state, action) => {
  state.isPaused = action.payload;
},
  }
});

export const { 
  showRecorder, 
  hideRecorder, 
  setRecordingState, 
  toggleExpanded,
  setAssesId,
  resetRecorder,
} = audioRecorderSlice.actions;

export default audioRecorderSlice.reducer;