import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

const KEY_STORAGE_KEY = 'secureStorageKey';
const SECURE_KEYS_KEY = 'scribbleKeys';

// Web-specific crypto utilities
class WebCrypto {
  private subtle = window.crypto.subtle;
  private encoder = new TextEncoder();
  private decoder = new TextDecoder();

  async generateKey(): Promise<CryptoKey> {
    return this.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);
  }

  async exportKey(key: CryptoKey): Promise<string> {
    const raw = await this.subtle.exportKey('raw', key);
    return btoa(String.fromCharCode(...new Uint8Array(raw)));
  }

  async importKey(base64: string): Promise<CryptoKey> {
    const raw = Uint8Array.from(atob(base64), c => c.charCodeAt(0));
    return this.subtle.importKey('raw', raw, 'AES-GCM', true, ['encrypt', 'decrypt']);
  }

  async getEncryptionKey(): Promise<CryptoKey> {
    let stored = await AsyncStorage.getItem(KEY_STORAGE_KEY);
    if (!stored) {
      const newKey = await this.generateKey();
      const exported = await this.exportKey(newKey);
      await AsyncStorage.setItem(KEY_STORAGE_KEY, exported);
      return newKey;
    }
    return this.importKey(stored);
  }

  async encrypt(value: string, key: CryptoKey): Promise<string> {
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encoded = this.encoder.encode(value);
    const encrypted = await this.subtle.encrypt({ name: 'AES-GCM', iv }, key, encoded);
    const result = new Uint8Array(iv.length + encrypted.byteLength);
    result.set(iv, 0);
    result.set(new Uint8Array(encrypted), iv.length);
    return btoa(String.fromCharCode(...result));
  }

  async decrypt(base64: string, key: CryptoKey): Promise<string> {
    const data = Uint8Array.from(atob(base64), c => c.charCodeAt(0));
    const iv = data.slice(0, 12);
    const ciphertext = data.slice(12);
    const decrypted = await this.subtle.decrypt({ name: 'AES-GCM', iv }, key, ciphertext);
    return this.decoder.decode(decrypted);
  }
}

// Storage adapters
interface StorageAdapter {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

class WebStorageAdapter implements StorageAdapter {
  private crypto = new WebCrypto();

  async setItem(key: string, value: string): Promise<void> {
    // const aesKey = await this.crypto.getEncryptionKey();
    // const encryptedValue = await this.crypto.encrypt(value, aesKey);
    await AsyncStorage.setItem(key, value);
  }

  async getItem(key: string): Promise<string | null> {
    
    // const encryptedValue = await AsyncStorage.getItem(key);
    // if (!encryptedValue) return null;
    // const aesKey = await this.crypto.getEncryptionKey();
    // return this.crypto.decrypt(encryptedValue, aesKey);
    return await AsyncStorage.getItem(key);
  }

  async removeItem(key: string): Promise<void> {
    await AsyncStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    await AsyncStorage.clear();
  }
}

class NativeStorageAdapter implements StorageAdapter {
  private secureStoreOptions = {
    keychainService: "scribble",
    accessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
  };

  async setItem(key: string, value: string): Promise<void> {
    // await SecureStore.setItemAsync(key, value,this.secureStoreOptions);
    // const keysJson = await SecureStore.getItemAsync(SECURE_KEYS_KEY,{ keychainService: "scribble" });
    // const keys = keysJson ? JSON.parse(keysJson) : [];
    // if (!keys.includes(key)) {
    //   keys.push(key);
    //   await SecureStore.setItemAsync(SECURE_KEYS_KEY, JSON.stringify(keys),this.secureStoreOptions);
    // }
    await AsyncStorage.setItem(key, value);
  }

  async getItem(key: string): Promise<string | null> {
   // return await SecureStore.getItemAsync(key,{ keychainService: "scribble" });
   return await AsyncStorage.getItem(key);
  }

  async removeItem(key: string): Promise<void> {
    //await SecureStore.deleteItemAsync(key,{ keychainService: "scribble" });
    await AsyncStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    //  await AsyncStorage.clear();
    // const keysJson = await SecureStore.getItemAsync(SECURE_KEYS_KEY,{ keychainService: "scribble" });
    // const keys = keysJson ? JSON.parse(keysJson) : [];

    // for (const key of keys) {
    //   await SecureStore.deleteItemAsync(key,{keychainService: "scribble"});
    // }
    // // Clear the key tracker
    // await SecureStore.deleteItemAsync(SECURE_KEYS_KEY,{keychainService: "scribble"});
     await AsyncStorage.clear();
  
  }
}

// Factory function
function createStorageAdapter(): StorageAdapter {
  return Platform.OS === 'web' ? new WebStorageAdapter() : new NativeStorageAdapter();
}

// Public API
const storage = createStorageAdapter();

export async function setSecureItem(key: string, value: string): Promise<void> {
  return storage.setItem(key, value);
}

export async function getSecureItem(key: string): Promise<string | null> {
  return storage.getItem(key);
}

export async function removeSecureItem(key: string): Promise<void> {
  return storage.removeItem(key);
}

export async function clearSecureStorage(): Promise<void> {
  return storage.clear();
}


