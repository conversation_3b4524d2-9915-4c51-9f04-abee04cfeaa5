// LoadingModal.tsx
import React, { useEffect, useState } from 'react';
import { Modal, ActivityIndicator, View, StyleSheet } from 'react-native';
import { loadingService } from '../api/apiClient';

const LoadingModal: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const unsubscribe = loadingService.subscribe((isLoading: boolean) => {

        console.log("LoadingModal - isLoading: ", isLoading);
      setIsVisible(isLoading);
    });

    return unsubscribe; // Cleanup subscription
  }, []);

  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={isVisible}
      statusBarTranslucent={true}
      onRequestClose={() => {}} // Prevent back button close
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalBackground}>
          <View style={styles.activityIndicatorWrapper}>
            <ActivityIndicator 
              size="large" 
              color="#3498db"
              style={styles.activityIndicator}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityIndicatorWrapper: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 20,
    minWidth: 80,
    minHeight: 80,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  activityIndicator: {
    marginBottom: 0,
  },
});

export default LoadingModal;