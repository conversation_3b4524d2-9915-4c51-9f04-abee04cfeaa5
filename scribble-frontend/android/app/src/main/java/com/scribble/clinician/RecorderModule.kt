package com.scribble.clinician


import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.*
import android.os.Build
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import java.io.File
import java.util.UUID

class RecorderModule(private val reactCtx: ReactApplicationContext)
    : ReactContextBaseJavaModule(reactCtx), AudioManager.OnAudioFocusChangeListener {

    private var mediaRecorder: MediaRecorder? = null
    private var outputFile: File? = null

    private var audioManager: AudioManager? = null
    private var focusRequest: AudioFocusRequest? = null
    private var wasPausedForFocusLoss = false

    private var noisyReceiverRegistered = false
    private val noisyReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            // Headphones unplugged etc. — treat as pause
            if (intent?.action == AudioManager.ACTION_AUDIO_BECOMING_NOISY) {
                pauseInternal(sendEvent = true)
            }
        }
    }

    override fun getName(): String = "RecorderModule"

    // ===== Public API exposed to JS =====

    @ReactMethod
    fun startRecording(promise: Promise) {
        if (mediaRecorder != null) {
            promise.reject("RECORD_ERR", "Recorder already active")
            return
        }

        try {
            audioManager = reactCtx.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // Request audio focus (Android equivalent of iOS interruptions)
            focusRequest = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val attrs = AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build()
                AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE)
                    .setAudioAttributes(attrs)
                    .setOnAudioFocusChangeListener(this)
                    .build()
            } else null

            val focusGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                audioManager?.requestAudioFocus(focusRequest!!) == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            } else {
                @Suppress("DEPRECATION")
                audioManager?.requestAudioFocus(
                    this, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE
                ) == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            }

            if (!focusGranted) {
                promise.reject("RECORD_ERR", "Audio focus not granted")
                return
            }

            // Output in app cache (like iOS temporaryDirectory)
            val dir = reactCtx.cacheDir
            val file = File(dir, "${UUID.randomUUID()}.m4a")
            outputFile = file

            val mr = MediaRecorder()
            mediaRecorder = mr

            mr.setAudioSource(MediaRecorder.AudioSource.MIC)
            mr.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
            mr.setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
            mr.setAudioSamplingRate(44100)
            mr.setAudioEncodingBitRate(128_000)
            mr.setAudioChannels(1)
            mr.setOutputFile(file.absolutePath)
            mr.prepare()
            mr.start()

            // Register “noisy” receiver (optional; helps emulate iOS interruption behavior)
            registerNoisyReceiver()

            promise.resolve("file://${file.absolutePath}")
        } catch (e: Exception) {
            releaseRecorder()
            promise.reject("RECORD_ERR", "Failed to start recording", e)
        }
    }

    @ReactMethod
    fun stopRecording(promise: Promise) {
        val file = outputFile
        val mr = mediaRecorder
        if (mr == null || file == null) {
            promise.reject("STOP_ERR", "No active recorder")
            return
        }

        try {
            mr.stop()
            promise.resolve("file://${file.absolutePath}")
        } catch (e: Exception) {
            promise.reject("STOP_ERR", "Failed to stop recording", e)
        } finally {
            releaseRecorder()
        }
    }

    @ReactMethod
    fun pauseRecording(promise: Promise) {
        val mr = mediaRecorder
        if (mr == null) {
            promise.reject("PAUSE_ERR", "No active recorder to pause")
            return
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            promise.reject("PAUSE_ERR", "Pause not supported below Android 7.0 (API 24)")
            return
        }
        try {
            mr.pause()
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("PAUSE_ERR", "Failed to pause", e)
        }
    }

    @ReactMethod
    fun deleteFile(uriString: String, promise: Promise) {
        try {
            // Determine the target from string
            val uri = android.net.Uri.parse(uriString)

            // If currently recording to this file, stop & clear refs
            fun isSameAsCurrentOutput(targetPath: String?): Boolean {
                val current = outputFile?.absolutePath
                return current != null && targetPath != null &&
                        File(current).absolutePath == File(targetPath).absolutePath
            }

            when (uri.scheme) {
                // file://... URI
                "file" -> {
                    val path = uri.path
                    if (isSameAsCurrentOutput(path)) {
                        try { mediaRecorder?.stop() } catch (_: Exception) {}
                        releaseRecorder()
                        outputFile = null
                    }

                    if (path.isNullOrEmpty()) {
                        promise.reject("DELETE_ERR", "Invalid file path (empty)")
                        return
                    }

                    val f = File(path)
                    if (f.exists()) {
                        val ok = f.delete()
                        val map = Arguments.createMap().apply {
                            putBoolean("deleted", ok)
                            putBoolean("existed", true)
                        }
                        promise.resolve(map)
                    } else {
                        val map = Arguments.createMap().apply {
                            putBoolean("deleted", false)
                            putBoolean("existed", false)
                        }
                        promise.resolve(map)
                    }
                }

                // content://... URI (e.g., if something passed a Content URI)
                "content" -> {
                    // We can’t directly compare paths reliably here.
                    // If you ONLY ever create files with File(cacheDir, ...),
                    // you likely won’t hit this branch. Still, handle it safely.
                    try {
                        // Best-effort: if we were recording, just release (no path compare)
                        // to avoid holding the FD/handle.
                        if (mediaRecorder != null) {
                            try { mediaRecorder?.stop() } catch (_: Exception) {}
                            releaseRecorder()
                            outputFile = null
                        }
                    } catch (_: Exception) {}

                    val rows = reactCtx.contentResolver.delete(uri, null, null)
                    val map = Arguments.createMap().apply {
                        putBoolean("deleted", rows > 0)
                        // For content URIs, we can’t cheaply check existence beforehand.
                        putBoolean("existed", rows > 0)
                    }
                    promise.resolve(map)
                }

                // Raw path (no scheme)
                null -> {
                    val path = uriString
                    if (isSameAsCurrentOutput(path)) {
                        try { mediaRecorder?.stop() } catch (_: Exception) {}
                        releaseRecorder()
                        outputFile = null
                    }

                    val f = File(path)
                    if (f.exists()) {
                        val ok = f.delete()
                        val map = Arguments.createMap().apply {
                            putBoolean("deleted", ok)
                            putBoolean("existed", true)
                        }
                        promise.resolve(map)
                    } else {
                        val map = Arguments.createMap().apply {
                            putBoolean("deleted", false)
                            putBoolean("existed", false)
                        }
                        promise.resolve(map)
                    }
                }

                // Any other scheme
                else -> {
                    promise.reject("DELETE_ERR", "Unsupported URI scheme: ${uri.scheme}")
                }
            }
        } catch (e: Exception) {
            promise.reject("DELETE_ERR", "Failed to delete: $uriString", e)
        }
    }


    @ReactMethod
    fun resumeRecording(promise: Promise) {
        val mr = mediaRecorder
        if (mr == null) {
            promise.reject("RESUME_ERR", "No paused recorder to resume")
            return
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            promise.reject("RESUME_ERR", "Resume not supported below Android 7.0 (API 24)")
            return
        }
        try {
            mr.resume()
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("RESUME_ERR", "Failed to resume", e)
        }
    }

    // ===== Audio focus callbacks (Android's "interruption") =====

    override fun onAudioFocusChange(focusChange: Int) {
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS,
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                // Pause and notify JS
                if (pauseInternal(sendEvent = true)) {
                    wasPausedForFocusLoss = true
                }
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                if (wasPausedForFocusLoss) {
                    if (resumeInternal(sendEvent = true)) {
                        wasPausedForFocusLoss = false
                    }
                }
            }
        }
    }

    // ===== Helpers =====

    private fun pauseInternal(sendEvent: Boolean): Boolean {
        val mr = mediaRecorder ?: return false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                mr.pause()
                if (sendEvent) sendInterruptionEvent("paused")
                return true
            } catch (_: Exception) {}
        }
        return false
    }

    private fun resumeInternal(sendEvent: Boolean): Boolean {
        val mr = mediaRecorder ?: return false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                mr.resume()
                if (sendEvent) sendInterruptionEvent("resumed")
                return true
            } catch (_: Exception) {}
        }
        return false
    }

    private fun sendInterruptionEvent(status: String) {
        val map = Arguments.createMap()
        map.putString("status", status)
        reactCtx
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit("RecordingInterruption", map)
    }

    private fun registerNoisyReceiver() {
        if (noisyReceiverRegistered) return
        val f = IntentFilter(AudioManager.ACTION_AUDIO_BECOMING_NOISY)
        reactCtx.registerReceiver(noisyReceiver, f)
        noisyReceiverRegistered = true
    }

    private fun unregisterNoisyReceiver() {
        if (!noisyReceiverRegistered) return
        try {
            reactCtx.unregisterReceiver(noisyReceiver)
        } catch (_: Exception) { }
        noisyReceiverRegistered = false
    }

    private fun releaseRecorder() {
        try {
            mediaRecorder?.reset()
        } catch (_: Exception) { }
        try {
            mediaRecorder?.release()
        } catch (_: Exception) { }
        mediaRecorder = null

        unregisterNoisyReceiver()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            focusRequest?.let { audioManager?.abandonAudioFocusRequest(it) }
        } else {
            @Suppress("DEPRECATION")
            audioManager?.abandonAudioFocus(this)
        }
        focusRequest = null
        audioManager = null
        wasPausedForFocusLoss = false
    }


}