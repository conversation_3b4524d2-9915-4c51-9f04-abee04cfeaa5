{"name": "scribble-frontend", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:dev": "EXPO_ENV=development expo start", "start:prod": "EXPO_ENV=production expo start", "android": "expo run:android", "android:dev": "EXPO_ENV=development expo start --android", "android:prod": "EXPO_ENV=production expo start --android", "ios": "expo run:ios", "ios:dev": "EXPO_ENV=development expo start --ios", "ios:prod": "EXPO_ENV=production expo start --ios", "web": "expo start --web", "web:dev": "EXPO_ENV=development expo start --web", "web:prod": "EXPO_ENV=production expo start --web", "build:web": "EXPO_ENV=production expo export -p web && npx workbox-cli generateSW workbox-config.js", "build:web:dev": "EXPO_ENV=development expo export -p web && npx workbox-cli generateSW workbox-config.js", "buildA": "cd android && ./gradlew assemblerelease && cd .."}, "dependencies": {"@babel/runtime": "^7.27.6", "@expo-google-fonts/plus-jakarta-sans": "^0.2.3", "@expo-google-fonts/poppins": "^0.2.3", "@expo/match-media": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "11.4.1", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/material-top-tabs": "^7.1.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.5.1", "@sentry/react-native": "~6.14.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "eventemitter3": "^5.0.1", "expo": "~53.0.0", "expo-asset": "~11.1.7", "expo-audio": "~0.4.8", "expo-av": "~15.1.7", "expo-checkbox": "^4.0.1", "expo-constants": "~17.1.7", "expo-device": "~7.1.4", "expo-document-picker": "^14.0.7", "expo-file-system": "^19.0.14", "expo-linear-gradient": "^14.0.2", "expo-localization": "~16.1.6", "expo-network": "~7.1.5", "expo-screen-orientation": "~8.1.7", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "i": "^0.3.7", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "react": "19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "19.0.0", "react-i18next": "^15.4.0", "react-native": "0.79.5", "react-native-device-info": "^14.0.4", "react-native-draggable": "^3.3.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-pager-view": "6.7.1", "react-native-paper": "^5.13.1", "react-native-paper-dates": "^0.22.34", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.17.4", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-web": "^0.20.0", "react-native-web-hover": "^0.2.9", "react-redux": "^9.2.0", "react-responsive": "^10.0.0", "redux-persist": "^6.0.0", "rn-sliding-up-panel": "^2.4.6", "undefined": "@sentry/react-native/expo"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react": "^16.2.0", "@testing-library/react-native": "^13.0.1", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "jest-expo": "~53.0.9", "prettier": "^3.4.2", "react-native-dotenv": "^3.4.11", "typescript": "^5.3.3"}, "private": true}